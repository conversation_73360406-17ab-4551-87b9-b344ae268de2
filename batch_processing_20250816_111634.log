2025-08-16 11:16:34,373 - INFO - === BATCH PROCESSING MODE INITIALIZED ===
2025-08-16 11:16:34,373 - INFO - Log file: batch_processing_20250816_111634.log
2025-08-16 11:16:34,373 - INFO - Directory exists: raw/
2025-08-16 11:16:34,373 - INFO - Directory exists: processed/
2025-08-16 11:16:34,373 - INFO - === STARTING BATCH ANALYSIS ===
2025-08-16 11:16:34,374 - INFO - Discovered 5 original TXT files for batch processing:
2025-08-16 11:16:34,374 - INFO -   1. 10075   34-3.TXT
2025-08-16 11:16:34,374 - INFO -   2. 10081   13-5.TXT
2025-08-16 11:16:34,374 - INFO -   3. 10107   6-2.TXT
2025-08-16 11:16:34,376 - INFO -   4. 10137   244-1.TXT
2025-08-16 11:16:34,376 - INFO -   5. 10173   67-6.TXT
2025-08-16 11:16:34,376 - INFO - Processing file 1/5: 10075   34-3.TXT (Pile ID: 10075   34-3)
2025-08-16 11:16:34,377 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10075   34-3 ===
2025-08-16 11:16:34,377 - INFO - [STAGE 1] Data extraction for 10075   34-3
2025-08-16 11:16:34,377 - INFO - Running: 数据导出 - 10075   34-3
2025-08-16 11:16:34,378 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 11:16:35,625 - INFO - [STAGE 1] SUCCESS: 10075   34-3_recreated.txt generated
2025-08-16 11:16:35,631 - INFO - [STAGE1] Moved 10075   34-3_recreated.txt to raw/
2025-08-16 11:16:35,631 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 11:16:35,632 - INFO - [STAGE 2] Data processing for 10075   34-3
2025-08-16 11:16:35,632 - INFO - Executing: 数据处理 - 10075   34-3 (reading from raw/)
2025-08-16 11:16:35,634 - INFO - Temporarily copied 10075   34-3_recreated.txt from raw/ to current directory
2025-08-16 11:16:35,634 - INFO - Running: 数据处理 - 10075   34-3
2025-08-16 11:16:35,634 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 11:16:37,909 - INFO - Cleaned up temporary file: 10075   34-3_recreated.txt
2025-08-16 11:16:37,909 - INFO - [STAGE 2] SUCCESS: 10075   34-3_processed.txt generated
2025-08-16 11:16:37,910 - INFO - [STAGE2] Moved 10075   34-3_processed.txt to processed/
2025-08-16 11:16:37,911 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 11:16:37,911 - INFO - [STAGE 3] Integrity analysis for 10075   34-3
2025-08-16 11:16:37,911 - INFO - Executing: 桩身完整性分析 - 10075   34-3 (reading from processed/)
2025-08-16 11:16:37,912 - INFO - Temporarily copied 10075   34-3_processed.txt from processed/ to current directory
2025-08-16 11:16:37,912 - INFO - Running: 桩身完整性分析 - 10075   34-3
2025-08-16 11:16:37,912 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 11:16:38,503 - INFO - Cleaned up temporary file: 10075   34-3_processed.txt
2025-08-16 11:16:38,503 - INFO - [STAGE 3] Completed for 10075   34-3
2025-08-16 11:16:38,518 - INFO - [RESULTS] Successfully extracted for 10075   34-3
2025-08-16 11:16:38,521 - INFO - [JSON] Successfully exported for 10075   34-3: pile_analysis_results_10075   34-3_20250816_111638.json
2025-08-16 11:16:38,521 - INFO - === PILE 10075   34-3 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 11:16:38,521 - INFO - SUCCESS: 10075   34-3 - Enhanced sequential processing completed
2025-08-16 11:16:38,522 - INFO - Processing file 2/5: 10081   13-5.TXT (Pile ID: 10081   13-5)
2025-08-16 11:16:38,523 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10081   13-5 ===
2025-08-16 11:16:38,523 - INFO - [STAGE 1] Data extraction for 10081   13-5
2025-08-16 11:16:38,523 - INFO - Running: 数据导出 - 10081   13-5
2025-08-16 11:16:38,524 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 11:16:44,738 - INFO - [STAGE 1] SUCCESS: 10081   13-5_recreated.txt generated
2025-08-16 11:16:44,750 - INFO - [STAGE1] Moved 10081   13-5_recreated.txt to raw/
2025-08-16 11:16:44,751 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 11:16:44,751 - INFO - [STAGE 2] Data processing for 10081   13-5
2025-08-16 11:16:44,751 - INFO - Executing: 数据处理 - 10081   13-5 (reading from raw/)
2025-08-16 11:16:44,765 - INFO - Temporarily copied 10081   13-5_recreated.txt from raw/ to current directory
2025-08-16 11:16:44,765 - INFO - Running: 数据处理 - 10081   13-5
2025-08-16 11:16:44,766 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 11:16:48,243 - INFO - Cleaned up temporary file: 10081   13-5_recreated.txt
2025-08-16 11:16:48,243 - INFO - [STAGE 2] SUCCESS: 10081   13-5_processed.txt generated
2025-08-16 11:16:48,244 - INFO - [STAGE2] Moved 10081   13-5_processed.txt to processed/
2025-08-16 11:16:48,244 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 11:16:48,244 - INFO - [STAGE 3] Integrity analysis for 10081   13-5
2025-08-16 11:16:48,244 - INFO - Executing: 桩身完整性分析 - 10081   13-5 (reading from processed/)
2025-08-16 11:16:48,244 - INFO - Temporarily copied 10081   13-5_processed.txt from processed/ to current directory
2025-08-16 11:16:48,245 - INFO - Running: 桩身完整性分析 - 10081   13-5
2025-08-16 11:16:48,245 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 11:16:48,830 - INFO - Cleaned up temporary file: 10081   13-5_processed.txt
2025-08-16 11:16:48,830 - INFO - [STAGE 3] Completed for 10081   13-5
2025-08-16 11:16:48,863 - INFO - [RESULTS] Successfully extracted for 10081   13-5
2025-08-16 11:16:48,865 - INFO - [JSON] Successfully exported for 10081   13-5: pile_analysis_results_10081   13-5_20250816_111648.json
2025-08-16 11:16:48,865 - INFO - === PILE 10081   13-5 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 11:16:48,865 - INFO - SUCCESS: 10081   13-5 - Enhanced sequential processing completed
2025-08-16 11:16:48,867 - INFO - Processing file 3/5: 10107   6-2.TXT (Pile ID: 10107   6-2)
2025-08-16 11:16:48,868 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10107   6-2 ===
2025-08-16 11:16:48,868 - INFO - [STAGE 1] Data extraction for 10107   6-2
2025-08-16 11:16:48,868 - INFO - Running: 数据导出 - 10107   6-2
2025-08-16 11:16:48,868 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 11:16:51,373 - INFO - [STAGE 1] SUCCESS: 10107   6-2_recreated.txt generated
2025-08-16 11:16:51,378 - INFO - [STAGE1] Moved 10107   6-2_recreated.txt to raw/
2025-08-16 11:16:51,379 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 11:16:51,379 - INFO - [STAGE 2] Data processing for 10107   6-2
2025-08-16 11:16:51,379 - INFO - Executing: 数据处理 - 10107   6-2 (reading from raw/)
2025-08-16 11:16:51,384 - INFO - Temporarily copied 10107   6-2_recreated.txt from raw/ to current directory
2025-08-16 11:16:51,384 - INFO - Running: 数据处理 - 10107   6-2
2025-08-16 11:16:51,384 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 11:16:54,190 - INFO - Cleaned up temporary file: 10107   6-2_recreated.txt
2025-08-16 11:16:54,190 - INFO - [STAGE 2] SUCCESS: 10107   6-2_processed.txt generated
2025-08-16 11:16:54,191 - INFO - [STAGE2] Moved 10107   6-2_processed.txt to processed/
2025-08-16 11:16:54,191 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 11:16:54,192 - INFO - [STAGE 3] Integrity analysis for 10107   6-2
2025-08-16 11:16:54,192 - INFO - Executing: 桩身完整性分析 - 10107   6-2 (reading from processed/)
2025-08-16 11:16:54,193 - INFO - Temporarily copied 10107   6-2_processed.txt from processed/ to current directory
2025-08-16 11:16:54,193 - INFO - Running: 桩身完整性分析 - 10107   6-2
2025-08-16 11:16:54,193 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 11:16:54,686 - INFO - Cleaned up temporary file: 10107   6-2_processed.txt
2025-08-16 11:16:54,686 - INFO - [STAGE 3] Completed for 10107   6-2
2025-08-16 11:16:54,706 - INFO - [RESULTS] Successfully extracted for 10107   6-2
2025-08-16 11:16:54,710 - INFO - [JSON] Successfully exported for 10107   6-2: pile_analysis_results_10107   6-2_20250816_111654.json
2025-08-16 11:16:54,710 - INFO - === PILE 10107   6-2 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 11:16:54,710 - INFO - SUCCESS: 10107   6-2 - Enhanced sequential processing completed
2025-08-16 11:16:54,712 - INFO - Processing file 4/5: 10137   244-1.TXT (Pile ID: 10137   244-1)
2025-08-16 11:16:54,712 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10137   244-1 ===
2025-08-16 11:16:54,712 - INFO - [STAGE 1] Data extraction for 10137   244-1
2025-08-16 11:16:54,712 - INFO - Running: 数据导出 - 10137   244-1
2025-08-16 11:16:54,713 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 11:16:55,765 - INFO - [STAGE 1] SUCCESS: 10137   244-1_recreated.txt generated
2025-08-16 11:16:55,768 - INFO - [STAGE1] Moved 10137   244-1_recreated.txt to raw/
2025-08-16 11:16:55,768 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 11:16:55,768 - INFO - [STAGE 2] Data processing for 10137   244-1
2025-08-16 11:16:55,769 - INFO - Executing: 数据处理 - 10137   244-1 (reading from raw/)
2025-08-16 11:16:55,770 - INFO - Temporarily copied 10137   244-1_recreated.txt from raw/ to current directory
2025-08-16 11:16:55,770 - INFO - Running: 数据处理 - 10137   244-1
2025-08-16 11:16:55,771 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 11:16:57,358 - INFO - Cleaned up temporary file: 10137   244-1_recreated.txt
2025-08-16 11:16:57,358 - INFO - [STAGE 2] SUCCESS: 10137   244-1_processed.txt generated
2025-08-16 11:16:57,359 - INFO - [STAGE2] Moved 10137   244-1_processed.txt to processed/
2025-08-16 11:16:57,359 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 11:16:57,360 - INFO - [STAGE 3] Integrity analysis for 10137   244-1
2025-08-16 11:16:57,360 - INFO - Executing: 桩身完整性分析 - 10137   244-1 (reading from processed/)
2025-08-16 11:16:57,361 - INFO - Temporarily copied 10137   244-1_processed.txt from processed/ to current directory
2025-08-16 11:16:57,361 - INFO - Running: 桩身完整性分析 - 10137   244-1
2025-08-16 11:16:57,361 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 11:16:57,871 - INFO - Cleaned up temporary file: 10137   244-1_processed.txt
2025-08-16 11:16:57,871 - INFO - [STAGE 3] Completed for 10137   244-1
2025-08-16 11:16:57,890 - INFO - [RESULTS] Successfully extracted for 10137   244-1
2025-08-16 11:16:57,892 - INFO - [JSON] Successfully exported for 10137   244-1: pile_analysis_results_10137   244-1_20250816_111657.json
2025-08-16 11:16:57,892 - INFO - === PILE 10137   244-1 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 11:16:57,892 - INFO - SUCCESS: 10137   244-1 - Enhanced sequential processing completed
2025-08-16 11:16:57,893 - INFO - Processing file 5/5: 10173   67-6.TXT (Pile ID: 10173   67-6)
2025-08-16 11:16:57,894 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10173   67-6 ===
2025-08-16 11:16:57,894 - INFO - [STAGE 1] Data extraction for 10173   67-6
2025-08-16 11:16:57,894 - INFO - Running: 数据导出 - 10173   67-6
2025-08-16 11:16:57,894 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 11:16:59,348 - INFO - [STAGE 1] SUCCESS: 10173   67-6_recreated.txt generated
2025-08-16 11:16:59,351 - INFO - [STAGE1] Moved 10173   67-6_recreated.txt to raw/
2025-08-16 11:16:59,352 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 11:16:59,352 - INFO - [STAGE 2] Data processing for 10173   67-6
2025-08-16 11:16:59,352 - INFO - Executing: 数据处理 - 10173   67-6 (reading from raw/)
2025-08-16 11:16:59,355 - INFO - Temporarily copied 10173   67-6_recreated.txt from raw/ to current directory
2025-08-16 11:16:59,355 - INFO - Running: 数据处理 - 10173   67-6
2025-08-16 11:16:59,355 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 11:17:01,404 - INFO - Cleaned up temporary file: 10173   67-6_recreated.txt
2025-08-16 11:17:01,404 - INFO - [STAGE 2] SUCCESS: 10173   67-6_processed.txt generated
2025-08-16 11:17:01,405 - INFO - [STAGE2] Moved 10173   67-6_processed.txt to processed/
2025-08-16 11:17:01,405 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 11:17:01,405 - INFO - [STAGE 3] Integrity analysis for 10173   67-6
2025-08-16 11:17:01,405 - INFO - Executing: 桩身完整性分析 - 10173   67-6 (reading from processed/)
2025-08-16 11:17:01,406 - INFO - Temporarily copied 10173   67-6_processed.txt from processed/ to current directory
2025-08-16 11:17:01,406 - INFO - Running: 桩身完整性分析 - 10173   67-6
2025-08-16 11:17:01,406 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 11:17:01,908 - INFO - Cleaned up temporary file: 10173   67-6_processed.txt
2025-08-16 11:17:01,909 - INFO - [STAGE 3] Completed for 10173   67-6
2025-08-16 11:17:01,924 - INFO - [RESULTS] Successfully extracted for 10173   67-6
2025-08-16 11:17:01,926 - INFO - [JSON] Successfully exported for 10173   67-6: pile_analysis_results_10173   67-6_20250816_111701.json
2025-08-16 11:17:01,926 - INFO - === PILE 10173   67-6 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 11:17:01,926 - INFO - SUCCESS: 10173   67-6 - Enhanced sequential processing completed
2025-08-16 11:17:01,927 - INFO - === BATCH PROCESSING SUMMARY REPORT ===
2025-08-16 11:17:01,927 - INFO - Total files processed: 5
2025-08-16 11:17:01,927 - INFO - Successful: 5
2025-08-16 11:17:01,927 - INFO - Failed: 0
2025-08-16 11:17:01,927 - INFO - Success rate: 100.0%
2025-08-16 11:17:01,928 - INFO - 
=== DETAILED RESULTS ===
