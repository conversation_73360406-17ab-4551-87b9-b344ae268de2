{"pile_id": "10075   34-3", "analysis_timestamp": "2025-08-16T09:24:10.535168", "analysis_method": "GZ Traditional Analysis Method", "pile_integrity_category": "II类桩\n\nGZ方法桩基完整性分析结果\n========================================\n\n最终判定: II类桩\n\n分析配置:\n- K值计算深度范围: 已启用 (0.5m / 50cm)\n- 启用指标: 声速, 波幅, 能量\n- 深度分析范围: 全部范围 (默认)\n- 判定依据: 新版GZ Traditio", "pile_integrity_category_type": 2, "statistical_summary": {"total_measurement_points": 436, "analysis_depth_range": {"min_depth": 0.69, "max_depth": 44.19, "depth_interval_count": 436}, "k_value_distribution_details": {"K1": {"count": 424, "percentage": 97.2, "depth_distribution": [0.69, 0.79, 0.89, 0.99, 1.09, 1.19, 1.39, 1.49, 1.59, 1.69, 1.79, 1.89, 1.99, 2.09, 2.19, 2.29, 2.49, 2.99, 3.09, 3.29, 3.39, 3.59, 3.69, 3.79, 3.89, 3.99, 4.09, 4.29, 4.39, 4.49, 4.59, 4.69, 4.79, 4.89, 4.99, 5.09, 5.19, 5.29, 5.39, 5.79, 5.89, 5.99, 6.09, 6.19, 6.29, 6.39, 6.49, 6.59, 6.69, 6.79, 6.89, 6.99, 7.09, 7.19, 7.29, 7.39, 7.49, 7.59, 7.69, 7.79, 7.89, 7.99, 8.09, 8.19, 8.29, 8.39, 8.49, 8.59, 8.69, 8.79, 8.89, 8.99, 9.09, 9.19, 9.29, 9.39, 9.49, 9.59, 9.69, 9.79, 9.89, 9.99, 10.09, 10.19, 10.29, 10.39, 10.49, 10.59, 10.69, 10.79, 10.89, 10.99, 11.09, 11.19, 11.29, 11.39, 11.49, 11.59, 11.69, 11.79, 11.89, 11.99, 12.09, 12.19, 12.29, 12.39, 12.49, 12.59, 12.69, 12.79, 12.89, 12.99, 13.09, 13.19, 13.29, 13.39, 13.49, 13.59, 13.69, 13.79, 13.89, 13.99, 14.09, 14.19, 14.29, 14.39, 14.49, 14.59, 14.69, 14.79, 14.89, 14.99, 15.09, 15.19, 15.29, 15.39, 15.49, 15.59, 15.69, 15.79, 15.89, 15.99, 16.09, 16.19, 16.29, 16.39, 16.49, 16.59, 16.69, 16.79, 16.89, 16.99, 17.09, 17.19, 17.29, 17.39, 17.49, 17.59, 17.69, 17.79, 17.89, 17.99, 18.09, 18.19, 18.29, 18.39, 18.49, 18.59, 18.69, 18.79, 18.89, 18.99, 19.09, 19.19, 19.29, 19.39, 19.49, 19.59, 19.69, 19.79, 19.89, 19.99, 20.09, 20.19, 20.29, 20.39, 20.49, 20.59, 20.69, 20.79, 20.89, 20.99, 21.09, 21.19, 21.29, 21.39, 21.49, 21.59, 21.69, 21.79, 21.89, 21.99, 22.09, 22.19, 22.29, 22.39, 22.49, 22.59, 22.69, 22.79, 22.89, 22.99, 23.09, 23.19, 23.29, 23.39, 23.49, 23.59, 23.69, 23.79, 23.89, 23.99, 24.09, 24.19, 24.29, 24.39, 24.49, 24.59, 24.69, 24.79, 24.89, 24.99, 25.09, 25.19, 25.29, 25.39, 25.49, 25.59, 25.69, 25.79, 25.89, 25.99, 26.09, 26.19, 26.29, 26.39, 26.49, 26.59, 26.69, 26.79, 26.89, 26.99, 27.09, 27.19, 27.29, 27.39, 27.49, 27.59, 27.69, 27.79, 27.89, 27.99, 28.09, 28.19, 28.29, 28.39, 28.49, 28.59, 28.69, 28.79, 28.89, 28.99, 29.09, 29.19, 29.29, 29.39, 29.49, 29.59, 29.69, 29.79, 29.89, 29.99, 30.09, 30.19, 30.29, 30.39, 30.49, 30.59, 30.69, 30.79, 30.89, 30.99, 31.09, 31.19, 31.29, 31.39, 31.49, 31.59, 31.69, 31.79, 31.89, 31.99, 32.09, 32.19, 32.29, 32.39, 32.49, 32.59, 32.69, 32.79, 32.89, 32.99, 33.09, 33.19, 33.29, 33.39, 33.49, 33.59, 33.69, 33.79, 33.89, 33.99, 34.09, 34.19, 34.29, 34.39, 34.49, 34.59, 34.69, 34.79, 34.89, 34.99, 35.09, 35.19, 35.29, 35.39, 35.49, 35.59, 35.69, 35.79, 35.89, 35.99, 36.09, 36.19, 36.29, 36.39, 36.49, 36.59, 36.69, 36.79, 36.89, 36.99, 37.09, 37.19, 37.29, 37.39, 37.49, 37.59, 37.69, 37.79, 37.89, 37.99, 38.09, 38.19, 38.29, 38.39, 38.49, 38.59, 38.69, 38.79, 38.89, 38.99, 39.09, 39.19, 39.29, 39.39, 39.49, 39.59, 39.69, 39.79, 39.89, 39.99, 40.09, 40.19, 40.29, 40.39, 40.49, 40.59, 40.69, 40.79, 40.89, 40.99, 41.09, 41.19, 41.29, 41.39, 41.49, 41.59, 41.69, 41.79, 41.89, 41.99, 42.09, 42.19, 42.29, 42.39, 42.49, 42.59, 42.69, 42.79, 42.89, 42.99, 43.09, 43.19, 43.29, 43.39, 43.49, 43.59, 43.69, 43.79, 43.89, 43.99, 44.09, 44.19]}, "K2": {"count": 12, "percentage": 2.8, "depth_distribution": [1.29, 2.39, 2.59, 2.69, 2.79, 2.89, 3.19, 3.49, 4.19, 5.49, 5.59, 5.69]}}}, "gz_analysis_results": {"final_assessment": "II类桩\n\n分析配置:\n- K值计算深度范围: 已启用 (0.5m / 50cm)\n- 启用指标: 声速, 波幅, 能量\n- 深度分析范围: 全部范围 (默认)\n- 判定依据: 新版GZ Traditio", "k_value_distribution_statistics": {"K1": {"count": 424, "percentage": 97.2}, "K2": {"count": 12, "percentage": 2.8}, "summary": {"total_measurement_points": 436, "k_value_type_count": 2, "max_k_value": 2, "min_k_value": 1}}, "depth_k_value_mapping": {"0.69": 1, "0.79": 1, "0.89": 1, "0.99": 1, "1.09": 1, "1.19": 1, "1.29": 2, "1.39": 1, "1.49": 1, "1.59": 1, "1.69": 1, "1.79": 1, "1.89": 1, "1.99": 1, "2.09": 1, "2.19": 1, "2.29": 1, "2.39": 2, "2.49": 1, "2.59": 2, "2.69": 2, "2.79": 2, "2.89": 2, "2.99": 1, "3.09": 1, "3.19": 2, "3.29": 1, "3.39": 1, "3.49": 2, "3.59": 1, "3.69": 1, "3.79": 1, "3.89": 1, "3.99": 1, "4.09": 1, "4.19": 2, "4.29": 1, "4.39": 1, "4.49": 1, "4.59": 1, "4.69": 1, "4.79": 1, "4.89": 1, "4.99": 1, "5.09": 1, "5.19": 1, "5.29": 1, "5.39": 1, "5.49": 2, "5.59": 2, "5.69": 2, "5.79": 1, "5.89": 1, "5.99": 1, "6.09": 1, "6.19": 1, "6.29": 1, "6.39": 1, "6.49": 1, "6.59": 1, "6.69": 1, "6.79": 1, "6.89": 1, "6.99": 1, "7.09": 1, "7.19": 1, "7.29": 1, "7.39": 1, "7.49": 1, "7.59": 1, "7.69": 1, "7.79": 1, "7.89": 1, "7.99": 1, "8.09": 1, "8.19": 1, "8.29": 1, "8.39": 1, "8.49": 1, "8.59": 1, "8.69": 1, "8.79": 1, "8.89": 1, "8.99": 1, "9.09": 1, "9.19": 1, "9.29": 1, "9.39": 1, "9.49": 1, "9.59": 1, "9.69": 1, "9.79": 1, "9.89": 1, "9.99": 1, "10.09": 1, "10.19": 1, "10.29": 1, "10.39": 1, "10.49": 1, "10.59": 1, "10.69": 1, "10.79": 1, "10.89": 1, "10.99": 1, "11.09": 1, "11.19": 1, "11.29": 1, "11.39": 1, "11.49": 1, "11.59": 1, "11.69": 1, "11.79": 1, "11.89": 1, "11.99": 1, "12.09": 1, "12.19": 1, "12.29": 1, "12.39": 1, "12.49": 1, "12.59": 1, "12.69": 1, "12.79": 1, "12.89": 1, "12.99": 1, "13.09": 1, "13.19": 1, "13.29": 1, "13.39": 1, "13.49": 1, "13.59": 1, "13.69": 1, "13.79": 1, "13.89": 1, "13.99": 1, "14.09": 1, "14.19": 1, "14.29": 1, "14.39": 1, "14.49": 1, "14.59": 1, "14.69": 1, "14.79": 1, "14.89": 1, "14.99": 1, "15.09": 1, "15.19": 1, "15.29": 1, "15.39": 1, "15.49": 1, "15.59": 1, "15.69": 1, "15.79": 1, "15.89": 1, "15.99": 1, "16.09": 1, "16.19": 1, "16.29": 1, "16.39": 1, "16.49": 1, "16.59": 1, "16.69": 1, "16.79": 1, "16.89": 1, "16.99": 1, "17.09": 1, "17.19": 1, "17.29": 1, "17.39": 1, "17.49": 1, "17.59": 1, "17.69": 1, "17.79": 1, "17.89": 1, "17.99": 1, "18.09": 1, "18.19": 1, "18.29": 1, "18.39": 1, "18.49": 1, "18.59": 1, "18.69": 1, "18.79": 1, "18.89": 1, "18.99": 1, "19.09": 1, "19.19": 1, "19.29": 1, "19.39": 1, "19.49": 1, "19.59": 1, "19.69": 1, "19.79": 1, "19.89": 1, "19.99": 1, "20.09": 1, "20.19": 1, "20.29": 1, "20.39": 1, "20.49": 1, "20.59": 1, "20.69": 1, "20.79": 1, "20.89": 1, "20.99": 1, "21.09": 1, "21.19": 1, "21.29": 1, "21.39": 1, "21.49": 1, "21.59": 1, "21.69": 1, "21.79": 1, "21.89": 1, "21.99": 1, "22.09": 1, "22.19": 1, "22.29": 1, "22.39": 1, "22.49": 1, "22.59": 1, "22.69": 1, "22.79": 1, "22.89": 1, "22.99": 1, "23.09": 1, "23.19": 1, "23.29": 1, "23.39": 1, "23.49": 1, "23.59": 1, "23.69": 1, "23.79": 1, "23.89": 1, "23.99": 1, "24.09": 1, "24.19": 1, "24.29": 1, "24.39": 1, "24.49": 1, "24.59": 1, "24.69": 1, "24.79": 1, "24.89": 1, "24.99": 1, "25.09": 1, "25.19": 1, "25.29": 1, "25.39": 1, "25.49": 1, "25.59": 1, "25.69": 1, "25.79": 1, "25.89": 1, "25.99": 1, "26.09": 1, "26.19": 1, "26.29": 1, "26.39": 1, "26.49": 1, "26.59": 1, "26.69": 1, "26.79": 1, "26.89": 1, "26.99": 1, "27.09": 1, "27.19": 1, "27.29": 1, "27.39": 1, "27.49": 1, "27.59": 1, "27.69": 1, "27.79": 1, "27.89": 1, "27.99": 1, "28.09": 1, "28.19": 1, "28.29": 1, "28.39": 1, "28.49": 1, "28.59": 1, "28.69": 1, "28.79": 1, "28.89": 1, "28.99": 1, "29.09": 1, "29.19": 1, "29.29": 1, "29.39": 1, "29.49": 1, "29.59": 1, "29.69": 1, "29.79": 1, "29.89": 1, "29.99": 1, "30.09": 1, "30.19": 1, "30.29": 1, "30.39": 1, "30.49": 1, "30.59": 1, "30.69": 1, "30.79": 1, "30.89": 1, "30.99": 1, "31.09": 1, "31.19": 1, "31.29": 1, "31.39": 1, "31.49": 1, "31.59": 1, "31.69": 1, "31.79": 1, "31.89": 1, "31.99": 1, "32.09": 1, "32.19": 1, "32.29": 1, "32.39": 1, "32.49": 1, "32.59": 1, "32.69": 1, "32.79": 1, "32.89": 1, "32.99": 1, "33.09": 1, "33.19": 1, "33.29": 1, "33.39": 1, "33.49": 1, "33.59": 1, "33.69": 1, "33.79": 1, "33.89": 1, "33.99": 1, "34.09": 1, "34.19": 1, "34.29": 1, "34.39": 1, "34.49": 1, "34.59": 1, "34.69": 1, "34.79": 1, "34.89": 1, "34.99": 1, "35.09": 1, "35.19": 1, "35.29": 1, "35.39": 1, "35.49": 1, "35.59": 1, "35.69": 1, "35.79": 1, "35.89": 1, "35.99": 1, "36.09": 1, "36.19": 1, "36.29": 1, "36.39": 1, "36.49": 1, "36.59": 1, "36.69": 1, "36.79": 1, "36.89": 1, "36.99": 1, "37.09": 1, "37.19": 1, "37.29": 1, "37.39": 1, "37.49": 1, "37.59": 1, "37.69": 1, "37.79": 1, "37.89": 1, "37.99": 1, "38.09": 1, "38.19": 1, "38.29": 1, "38.39": 1, "38.49": 1, "38.59": 1, "38.69": 1, "38.79": 1, "38.89": 1, "38.99": 1, "39.09": 1, "39.19": 1, "39.29": 1, "39.39": 1, "39.49": 1, "39.59": 1, "39.69": 1, "39.79": 1, "39.89": 1, "39.99": 1, "40.09": 1, "40.19": 1, "40.29": 1, "40.39": 1, "40.49": 1, "40.59": 1, "40.69": 1, "40.79": 1, "40.89": 1, "40.99": 1, "41.09": 1, "41.19": 1, "41.29": 1, "41.39": 1, "41.49": 1, "41.59": 1, "41.69": 1, "41.79": 1, "41.89": 1, "41.99": 1, "42.09": 1, "42.19": 1, "42.29": 1, "42.39": 1, "42.49": 1, "42.59": 1, "42.69": 1, "42.79": 1, "42.89": 1, "42.99": 1, "43.09": 1, "43.19": 1, "43.29": 1, "43.39": 1, "43.49": 1, "43.59": 1, "43.69": 1, "43.79": 1, "43.89": 1, "43.99": 1, "44.09": 1, "44.19": 1}, "max_k_depth": 1.29, "max_k_value": 2, "critical_depth_assessment": {}, "iji_value_details": {}, "raw_parameter_data": {}, "assessment_criteria": ["K值分布统计:", "K=1: 424个截面 (97.2%)", "K=2: 12个截面 (2.8%)", "总计分析截面: 436个", "最大K值位置: 深度1.29m处，K值=2", "相同最大K值出现位置: 1.29m, 2.39m, 2.59m, 2.69m, 2.79m, 2.89m, 3.19m, 3.49m, 4.19m, 5.49m, 5.59m, 5.69m", "多个K=2但不存在50cm连续范围，位于深度: 5.69m, 5.59m, 5.49m, 4.19m, 3.49m, 3.19m, 2.89m, 2.79m, 2.69m, 2.59m, 2.39m, 1.29m。"], "analysis_configuration": ["K值计算深度范围: 已启用 (0.5m / 50cm)", "启用指标: 声速, 波幅, 能量", "深度分析范围: 全部范围 (默认)", "判定依据: 新版GZ Traditional Analysis分类规则"]}, "source_file": "pile_integrity_analysis_20250816_092410.txt", "data_version": "v2.0_enhanced", "export_info": {"export_time": "2025-08-16T09:24:10.537115", "export_version": "v2.0_enhanced", "system_info": {"analysis_engine": "GZ Traditional Analysis Method", "data_processing_version": "2025 Standard", "json_format_version": "2.0"}}}