#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pile Integrity Category Distribution Aggregator (Recreated)

Primary Objective:
- Load all JSON files from the target directory
- Extract pile integrity category for each file (I, II, III, IV)
- Compute counts and percentage distribution across all JSON files
- Output readable report to console and to a summary file with '_recreated' suffix

Technical Notes:
- Uses only Python standard libraries (json, logging, argparse, pathlib)
- Robust error handling for I/O and JSON decoding issues
- Comprehensive logging to a timestamped log file
- Preserves all source files; writes summary into an output directory

Usage:
  python pile_integrity_distribution_recreated.py --dir .

"""
from __future__ import annotations

import argparse
import json
import logging
import os
import re
import sys
import time
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

ROMAN_BY_INT = {1: "I", 2: "II", 3: "III", 4: "IV"}
INT_BY_ROMAN = {v: k for k, v in ROMAN_BY_INT.items()}
CHINESE_PATTERN_ORDERED = [
    ("IV", ["IV类桩", "IV类", "iv", "iv类桩", "四类", "4类", "4"]),
    ("III", ["III类桩", "III类", "iii", "iii类桩", "三类", "3类", "3"]),
    ("II", ["II类桩", "II类", "ii", "ii类桩", "二类", "2类", "2"]),
    ("I", ["I类桩", "I类", "i", "i类桩", "一类", "1类", "1"]),
]

@dataclass
class FileResult:
    filename: str
    category: Optional[str]
    error: Optional[str]


def setup_logging(logs_dir: Path) -> Tuple[logging.Logger, Path]:
    logs_dir.mkdir(parents=True, exist_ok=True)
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_path = logs_dir / f"pile_integrity_distribution_recreated_{ts}.log"

    logger = logging.getLogger("pile_integrity_distribution")
    logger.setLevel(logging.INFO)

    # Avoid duplicate handlers if script run multiple times in same interpreter
    if logger.handlers:
        for h in list(logger.handlers):
            logger.removeHandler(h)

    fh = logging.FileHandler(log_path, encoding="utf-8")
    sh = logging.StreamHandler(sys.stdout)

    fmt = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    fh.setFormatter(fmt)
    sh.setFormatter(fmt)

    logger.addHandler(fh)
    logger.addHandler(sh)

    logger.info("=== Pile Integrity Category Distribution (Recreated) ===")
    logger.info(f"Log file: {log_path.name}")

    return logger, log_path


def find_json_files(target_dir: Path) -> List[Path]:
    files = sorted(target_dir.glob("*.json"))
    return files


def read_json(path: Path, logger: logging.Logger) -> Optional[dict]:
    # Try utf-8-sig then gbk as fallback
    for enc in ("utf-8-sig", "utf-8", "gbk"):
        try:
            with path.open("r", encoding=enc) as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error in {path.name} (encoding {enc}): {e}")
            return None
        except UnicodeDecodeError:
            # Try next encoding
            continue
        except Exception as e:
            logger.error(f"Error reading {path.name}: {e}")
            return None
    logger.error(f"Failed to decode {path.name} with known encodings")
    return None


def extract_category(obj: dict, logger: logging.Logger) -> Optional[str]:
    """Extract canonical category 'I'|'II'|'III'|'IV' from a JSON object.
    Priority:
      1) pile_integrity_category_type as int 1..4
      2) pile_integrity_category string containing tokens like 'II类桩' or '最终判定: II类桩'
      3) Deep search string values for indicators (Chinese/roman/numeric)
    """
    # 1) Direct numeric type
    try:
        t = obj.get("pile_integrity_category_type")
        if isinstance(t, int) and t in ROMAN_BY_INT:
            return ROMAN_BY_INT[t]
        # Sometimes numeric as string
        if isinstance(t, str):
            t_clean = t.strip()
            if t_clean.isdigit():
                val = int(t_clean)
                if val in ROMAN_BY_INT:
                    return ROMAN_BY_INT[val]
    except Exception:
        pass

    # 2) Specific string field
    s = obj.get("pile_integrity_category")
    if isinstance(s, str):
        cat = parse_category_from_text(s)
        if cat:
            return cat

    # 3) Deep search over all string values
    def walk(v) -> Optional[str]:
        if isinstance(v, dict):
            for vv in v.values():
                r = walk(vv)
                if r:
                    return r
        elif isinstance(v, list):
            for vv in v:
                r = walk(vv)
                if r:
                    return r
        elif isinstance(v, str):
            r = parse_category_from_text(v)
            if r:
                return r
        return None

    return walk(obj)


def parse_category_from_text(text: str) -> Optional[str]:
    t = text.strip().lower()
    # Quick ordered checks to avoid mis-detection (e.g., 'iv' inside words)
    for roman, patterns in CHINESE_PATTERN_ORDERED:
        for pat in patterns:
            if pat in t:
                return roman
    # Regex fallback for standalone roman numerals I, II, III, IV
    m = re.search(r"\b(iv|iii|ii|i)\b", t)
    if m:
        return m.group(1).upper()
    return None


def format_distribution(counts: Dict[str, int], denominator: int) -> str:
    lines = []
    header = "Category  Count  Percentage"
    lines.append(header)
    lines.append("-" * len(header))
    for cat in ("I", "II", "III", "IV"):
        c = counts.get(cat, 0)
        pct = (c / denominator * 100.0) if denominator > 0 else 0.0
        lines.append(f"{cat:<8} {c:>5}  {pct:>9.2f}%")
    return "\n".join(lines)


def write_summary(out_dir: Path, results: List[FileResult], counts: Dict[str, int], total_json: int, elapsed_s: float) -> Path:
    out_dir.mkdir(parents=True, exist_ok=True)
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    out_path = out_dir / f"pile_integrity_summary_recreated_{ts}.txt"

    processed = sum(1 for r in results if r.category is not None)
    errors = sum(1 for r in results if r.error is not None)
    skipped = total_json - processed  # includes errors and any without category

    lines: List[str] = []
    lines.append("Pile Integrity Category Distribution Summary (Recreated)")
    lines.append("=" * 60)
    lines.append(f"Timestamp: {datetime.now().isoformat(timespec='seconds')}")
    lines.append(f"Directory: {out_dir.parent.resolve()}")
    lines.append("")
    lines.append(f"Total JSON files: {total_json}")
    lines.append(f"Processed (with category): {processed}")
    lines.append(f"Errors: {errors}")
    lines.append(f"Elapsed: {elapsed_s:.2f}s")
    lines.append("")
    lines.append("Distribution across ALL JSON files (denominator = total JSON):")
    lines.append(format_distribution(counts, denominator=total_json))
    lines.append("")
    lines.append("Problematic files:")
    if errors == 0:
        lines.append("  (none)")
    else:
        for r in results:
            if r.error:
                lines.append(f"  - {r.filename}: {r.error}")

    out_text = "\n".join(lines) + "\n"
    out_path.write_text(out_text, encoding="utf-8")
    return out_path


def run(directory: Path, output_dir: Path, logs_dir: Path) -> int:
    start = time.time()
    logger, log_path = setup_logging(logs_dir)

    logger.info(f"Scanning directory for JSON files: {directory}")
    json_files = find_json_files(directory)
    logger.info(f"Found {len(json_files)} JSON files")

    counts: Dict[str, int] = {"I": 0, "II": 0, "III": 0, "IV": 0}
    results: List[FileResult] = []

    for i, path in enumerate(json_files, 1):
        logger.info(f"[{i}/{len(json_files)}] Processing {path.name}")
        data = read_json(path, logger)
        if data is None:
            results.append(FileResult(path.name, None, "Malformed or unreadable JSON"))
            continue
        try:
            cat = extract_category(data, logger)
            if cat in counts:
                counts[cat] += 1
                results.append(FileResult(path.name, cat, None))
                logger.info(f"  -> Category detected: {cat}")
            else:
                results.append(FileResult(path.name, None, "Category not found"))
                logger.warning("  -> Category not found in JSON")
        except Exception as e:
            msg = f"Unexpected error while extracting category: {e}"
            results.append(FileResult(path.name, None, msg))
            logger.error("  -> " + msg)

    elapsed_s = time.time() - start
    summary_path = write_summary(output_dir, results, counts, total_json=len(json_files), elapsed_s=elapsed_s)

    logger.info("=== Summary ===")
    logger.info(summary_path.read_text(encoding="utf-8"))
    logger.info(f"Summary saved to: {summary_path}")
    logger.info(f"Log saved to: {log_path}")

    # Also print a concise console summary
    print("\n" + summary_path.read_text(encoding="utf-8"))
    print(f"Summary saved to: {summary_path}")
    print(f"Log saved to: {log_path}")

    return 0


def parse_args(argv: List[str]) -> argparse.Namespace:
    p = argparse.ArgumentParser(description="Aggregate pile integrity categories from JSON files")
    p.add_argument("--dir", default=".", help="Directory to scan for JSON files (default: current directory)")
    p.add_argument("--output-dir", default="output", help="Directory to write summary files (default: output)")
    p.add_argument("--logs-dir", default="logs", help="Directory to write log files (default: logs)")
    return p.parse_args(argv)


if __name__ == "__main__":
    ns = parse_args(sys.argv[1:])
    directory = Path(ns.dir).resolve()
    output_dir = Path(ns.output_dir)
    logs_dir = Path(ns.logs_dir)

    # Ensure we operate in the provided directory for discovery but write outputs in project root subdirs
    try:
        sys.exit(run(directory, output_dir, logs_dir))
    except KeyboardInterrupt:
        print("Interrupted by user.")
        sys.exit(130)

