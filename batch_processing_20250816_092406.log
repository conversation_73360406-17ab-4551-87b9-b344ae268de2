2025-08-16 09:24:06,007 - INFO - === BATCH PROCESSING MODE INITIALIZED ===
2025-08-16 09:24:06,008 - INFO - Log file: batch_processing_20250816_092406.log
2025-08-16 09:24:06,008 - INFO - Directory exists: raw/
2025-08-16 09:24:06,008 - INFO - Directory exists: processed/
2025-08-16 09:24:06,008 - INFO - === STARTING BATCH ANALYSIS ===
2025-08-16 09:24:06,009 - INFO - Discovered 5 original TXT files for batch processing:
2025-08-16 09:24:06,009 - INFO -   1. 10075   34-3.TXT
2025-08-16 09:24:06,009 - INFO -   2. 10081   13-5.TXT
2025-08-16 09:24:06,009 - INFO -   3. 10107   6-2.TXT
2025-08-16 09:24:06,017 - INFO -   4. 10137   244-1.TXT
2025-08-16 09:24:06,017 - INFO -   5. 10173   67-6.TXT
2025-08-16 09:24:06,018 - INFO - Processing file 1/5: 10075   34-3.TXT (Pile ID: 10075   34-3)
2025-08-16 09:24:06,020 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10075   34-3 ===
2025-08-16 09:24:06,021 - INFO - [STAGE 1] Data extraction for 10075   34-3
2025-08-16 09:24:06,021 - INFO - Running: 数据导出 - 10075   34-3
2025-08-16 09:24:06,021 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 09:24:07,423 - INFO - [STAGE 1] SUCCESS: 10075   34-3_recreated.txt generated
2025-08-16 09:24:07,428 - INFO - [STAGE1] Moved 10075   34-3_recreated.txt to raw/
2025-08-16 09:24:07,429 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 09:24:07,429 - INFO - [STAGE 2] Data processing for 10075   34-3
2025-08-16 09:24:07,429 - INFO - Executing: 数据处理 - 10075   34-3 (reading from raw/)
2025-08-16 09:24:07,432 - INFO - Temporarily copied 10075   34-3_recreated.txt from raw/ to current directory
2025-08-16 09:24:07,432 - INFO - Running: 数据处理 - 10075   34-3
2025-08-16 09:24:07,432 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 09:24:09,763 - INFO - Cleaned up temporary file: 10075   34-3_recreated.txt
2025-08-16 09:24:09,763 - INFO - [STAGE 2] SUCCESS: 10075   34-3_processed.txt generated
2025-08-16 09:24:09,764 - INFO - [STAGE2] Moved 10075   34-3_processed.txt to processed/
2025-08-16 09:24:09,764 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 09:24:09,764 - INFO - [STAGE 3] Integrity analysis for 10075   34-3
2025-08-16 09:24:09,764 - INFO - Executing: 桩身完整性分析 - 10075   34-3 (reading from processed/)
2025-08-16 09:24:09,764 - INFO - Temporarily copied 10075   34-3_processed.txt from processed/ to current directory
2025-08-16 09:24:09,765 - INFO - Running: 桩身完整性分析 - 10075   34-3
2025-08-16 09:24:09,765 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 09:24:10,519 - INFO - Cleaned up temporary file: 10075   34-3_processed.txt
2025-08-16 09:24:10,519 - INFO - [STAGE 3] Completed for 10075   34-3
2025-08-16 09:24:10,536 - INFO - [RESULTS] Successfully extracted for 10075   34-3
2025-08-16 09:24:10,539 - INFO - [JSON] Successfully exported for 10075   34-3: pile_analysis_results_10075   34-3_20250816_092410.json
2025-08-16 09:24:10,539 - INFO - === PILE 10075   34-3 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 09:24:10,539 - INFO - SUCCESS: 10075   34-3 - Enhanced sequential processing completed
2025-08-16 09:24:10,542 - INFO - Processing file 2/5: 10081   13-5.TXT (Pile ID: 10081   13-5)
2025-08-16 09:24:10,542 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10081   13-5 ===
2025-08-16 09:24:10,543 - INFO - [STAGE 1] Data extraction for 10081   13-5
2025-08-16 09:24:10,543 - INFO - Running: 数据导出 - 10081   13-5
2025-08-16 09:24:10,543 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 09:24:17,310 - INFO - [STAGE 1] SUCCESS: 10081   13-5_recreated.txt generated
2025-08-16 09:24:17,325 - INFO - [STAGE1] Moved 10081   13-5_recreated.txt to raw/
2025-08-16 09:24:17,325 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 09:24:17,326 - INFO - [STAGE 2] Data processing for 10081   13-5
2025-08-16 09:24:17,326 - INFO - Executing: 数据处理 - 10081   13-5 (reading from raw/)
2025-08-16 09:24:17,336 - INFO - Temporarily copied 10081   13-5_recreated.txt from raw/ to current directory
2025-08-16 09:24:17,336 - INFO - Running: 数据处理 - 10081   13-5
2025-08-16 09:24:17,336 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 09:24:21,278 - INFO - Cleaned up temporary file: 10081   13-5_recreated.txt
2025-08-16 09:24:21,278 - INFO - [STAGE 2] SUCCESS: 10081   13-5_processed.txt generated
2025-08-16 09:24:21,279 - ERROR - Error organizing files for 10081   13-5 at stage2: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'E:\\桩基完整性系统\\final_test\\07-05\\testing III - 副本 - 副本 - 副本\\10081   13-5_processed.txt'
2025-08-16 09:24:21,279 - INFO - [STAGE 2] File organization: 0 files moved to processed/
2025-08-16 09:24:21,279 - INFO - [STAGE 3] Integrity analysis for 10081   13-5
2025-08-16 09:24:21,280 - INFO - Executing: 桩身完整性分析 - 10081   13-5 (reading from processed/)
2025-08-16 09:24:21,292 - INFO - Temporarily copied 10081   13-5_processed.txt from processed/ to current directory
2025-08-16 09:24:21,292 - INFO - Running: 桩身完整性分析 - 10081   13-5
2025-08-16 09:24:21,292 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 09:24:22,025 - INFO - Cleaned up temporary file: 10081   13-5_processed.txt
2025-08-16 09:24:22,025 - INFO - [STAGE 3] Completed for 10081   13-5
2025-08-16 09:24:22,066 - INFO - [RESULTS] Successfully extracted for 10081   13-5
2025-08-16 09:24:22,069 - INFO - [JSON] Successfully exported for 10081   13-5: pile_analysis_results_10081   13-5_20250816_092422.json
2025-08-16 09:24:22,069 - INFO - === PILE 10081   13-5 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 09:24:22,069 - INFO - SUCCESS: 10081   13-5 - Enhanced sequential processing completed
2025-08-16 09:24:22,070 - INFO - Processing file 3/5: 10107   6-2.TXT (Pile ID: 10107   6-2)
2025-08-16 09:24:22,071 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10107   6-2 ===
2025-08-16 09:24:22,071 - INFO - [STAGE 1] Data extraction for 10107   6-2
2025-08-16 09:24:22,071 - INFO - Running: 数据导出 - 10107   6-2
2025-08-16 09:24:22,071 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 09:24:24,768 - INFO - [STAGE 1] SUCCESS: 10107   6-2_recreated.txt generated
2025-08-16 09:24:24,776 - INFO - [STAGE1] Moved 10107   6-2_recreated.txt to raw/
2025-08-16 09:24:24,776 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 09:24:24,776 - INFO - [STAGE 2] Data processing for 10107   6-2
2025-08-16 09:24:24,776 - INFO - Executing: 数据处理 - 10107   6-2 (reading from raw/)
2025-08-16 09:24:24,782 - INFO - Temporarily copied 10107   6-2_recreated.txt from raw/ to current directory
2025-08-16 09:24:24,782 - INFO - Running: 数据处理 - 10107   6-2
2025-08-16 09:24:24,783 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 09:24:27,885 - INFO - Cleaned up temporary file: 10107   6-2_recreated.txt
2025-08-16 09:24:27,886 - INFO - [STAGE 2] SUCCESS: 10107   6-2_processed.txt generated
2025-08-16 09:24:27,887 - ERROR - Error organizing files for 10107   6-2 at stage2: [WinError 32] 另一个程序正在使用此文件，进程无法访问。
2025-08-16 09:24:27,887 - INFO - [STAGE 2] File organization: 0 files moved to processed/
2025-08-16 09:24:27,887 - INFO - [STAGE 3] Integrity analysis for 10107   6-2
2025-08-16 09:24:27,887 - INFO - Executing: 桩身完整性分析 - 10107   6-2 (reading from processed/)
2025-08-16 09:24:27,890 - INFO - Temporarily copied 10107   6-2_processed.txt from processed/ to current directory
2025-08-16 09:24:27,890 - INFO - Running: 桩身完整性分析 - 10107   6-2
2025-08-16 09:24:27,890 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 09:24:28,542 - INFO - Cleaned up temporary file: 10107   6-2_processed.txt
2025-08-16 09:24:28,542 - INFO - [STAGE 3] Completed for 10107   6-2
2025-08-16 09:24:28,555 - INFO - [RESULTS] Successfully extracted for 10107   6-2
2025-08-16 09:24:28,560 - INFO - [JSON] Successfully exported for 10107   6-2: pile_analysis_results_10107   6-2_20250816_092428.json
2025-08-16 09:24:28,561 - INFO - === PILE 10107   6-2 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 09:24:28,561 - INFO - SUCCESS: 10107   6-2 - Enhanced sequential processing completed
2025-08-16 09:24:28,564 - INFO - Processing file 4/5: 10137   244-1.TXT (Pile ID: 10137   244-1)
2025-08-16 09:24:28,565 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10137   244-1 ===
2025-08-16 09:24:28,565 - INFO - [STAGE 1] Data extraction for 10137   244-1
2025-08-16 09:24:28,565 - INFO - Running: 数据导出 - 10137   244-1
2025-08-16 09:24:28,566 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 09:24:29,713 - INFO - [STAGE 1] SUCCESS: 10137   244-1_recreated.txt generated
2025-08-16 09:24:29,718 - INFO - [STAGE1] Moved 10137   244-1_recreated.txt to raw/
2025-08-16 09:24:29,718 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 09:24:29,718 - INFO - [STAGE 2] Data processing for 10137   244-1
2025-08-16 09:24:29,718 - INFO - Executing: 数据处理 - 10137   244-1 (reading from raw/)
2025-08-16 09:24:29,721 - INFO - Temporarily copied 10137   244-1_recreated.txt from raw/ to current directory
2025-08-16 09:24:29,721 - INFO - Running: 数据处理 - 10137   244-1
2025-08-16 09:24:29,722 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 09:24:31,677 - INFO - Cleaned up temporary file: 10137   244-1_recreated.txt
2025-08-16 09:24:31,677 - INFO - [STAGE 2] SUCCESS: 10137   244-1_processed.txt generated
2025-08-16 09:24:31,677 - INFO - [STAGE2] Moved 10137   244-1_processed.txt to processed/
2025-08-16 09:24:31,678 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 09:24:31,678 - INFO - [STAGE 3] Integrity analysis for 10137   244-1
2025-08-16 09:24:31,678 - INFO - Executing: 桩身完整性分析 - 10137   244-1 (reading from processed/)
2025-08-16 09:24:31,679 - INFO - Temporarily copied 10137   244-1_processed.txt from processed/ to current directory
2025-08-16 09:24:31,679 - INFO - Running: 桩身完整性分析 - 10137   244-1
2025-08-16 09:24:31,679 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 09:24:32,275 - INFO - Cleaned up temporary file: 10137   244-1_processed.txt
2025-08-16 09:24:32,275 - INFO - [STAGE 3] Completed for 10137   244-1
2025-08-16 09:24:32,289 - INFO - [RESULTS] Successfully extracted for 10137   244-1
2025-08-16 09:24:32,291 - INFO - [JSON] Successfully exported for 10137   244-1: pile_analysis_results_10137   244-1_20250816_092432.json
2025-08-16 09:24:32,291 - INFO - === PILE 10137   244-1 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 09:24:32,291 - INFO - SUCCESS: 10137   244-1 - Enhanced sequential processing completed
2025-08-16 09:24:32,294 - INFO - Processing file 5/5: 10173   67-6.TXT (Pile ID: 10173   67-6)
2025-08-16 09:24:32,294 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10173   67-6 ===
2025-08-16 09:24:32,294 - INFO - [STAGE 1] Data extraction for 10173   67-6
2025-08-16 09:24:32,295 - INFO - Running: 数据导出 - 10173   67-6
2025-08-16 09:24:32,295 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 09:24:33,841 - INFO - [STAGE 1] SUCCESS: 10173   67-6_recreated.txt generated
2025-08-16 09:24:33,846 - INFO - [STAGE1] Moved 10173   67-6_recreated.txt to raw/
2025-08-16 09:24:33,846 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 09:24:33,846 - INFO - [STAGE 2] Data processing for 10173   67-6
2025-08-16 09:24:33,846 - INFO - Executing: 数据处理 - 10173   67-6 (reading from raw/)
2025-08-16 09:24:33,850 - INFO - Temporarily copied 10173   67-6_recreated.txt from raw/ to current directory
2025-08-16 09:24:33,851 - INFO - Running: 数据处理 - 10173   67-6
2025-08-16 09:24:33,851 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 09:24:36,281 - INFO - Cleaned up temporary file: 10173   67-6_recreated.txt
2025-08-16 09:24:36,282 - INFO - [STAGE 2] SUCCESS: 10173   67-6_processed.txt generated
2025-08-16 09:24:36,282 - INFO - [STAGE2] Moved 10173   67-6_processed.txt to processed/
2025-08-16 09:24:36,282 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 09:24:36,282 - INFO - [STAGE 3] Integrity analysis for 10173   67-6
2025-08-16 09:24:36,282 - INFO - Executing: 桩身完整性分析 - 10173   67-6 (reading from processed/)
2025-08-16 09:24:36,283 - INFO - Temporarily copied 10173   67-6_processed.txt from processed/ to current directory
2025-08-16 09:24:36,283 - INFO - Running: 桩身完整性分析 - 10173   67-6
2025-08-16 09:24:36,283 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 09:24:36,874 - INFO - Cleaned up temporary file: 10173   67-6_processed.txt
2025-08-16 09:24:36,874 - INFO - [STAGE 3] Completed for 10173   67-6
2025-08-16 09:24:36,890 - INFO - [RESULTS] Successfully extracted for 10173   67-6
2025-08-16 09:24:36,892 - INFO - [JSON] Successfully exported for 10173   67-6: pile_analysis_results_10173   67-6_20250816_092436.json
2025-08-16 09:24:36,892 - INFO - === PILE 10173   67-6 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 09:24:36,893 - INFO - SUCCESS: 10173   67-6 - Enhanced sequential processing completed
2025-08-16 09:24:36,896 - INFO - === BATCH PROCESSING SUMMARY REPORT ===
2025-08-16 09:24:36,897 - INFO - Total files processed: 5
2025-08-16 09:24:36,897 - INFO - Successful: 5
2025-08-16 09:24:36,897 - INFO - Failed: 0
2025-08-16 09:24:36,898 - INFO - Success rate: 100.0%
2025-08-16 09:24:36,898 - INFO - 
=== DETAILED RESULTS ===
