{"pile_id": "10081   13-5", "analysis_timestamp": "2025-08-16T09:24:22.065391", "analysis_method": "GZ Traditional Analysis Method", "pile_integrity_category": "III类桩\n\nGZ方法桩基完整性分析结果\n========================================\n\n最终判定: III类桩\n\n分析配置:\n- K值计算深度范围: 已启用 (0.5m / 50cm)\n- 启用指标: 声速, 波幅, 能量\n- 深度分析范围: 全部范围 (默认)\n- 判定依据: 新版GZ Traditio", "pile_integrity_category_type": 3, "statistical_summary": {"total_measurement_points": 451, "analysis_depth_range": {"min_depth": 0.6, "max_depth": 45.6, "depth_interval_count": 451}, "k_value_distribution_details": {"K1": {"count": 435, "percentage": 96.5, "depth_distribution": [2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 4.0, 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 4.8, 4.9, 5.0, 5.1, 5.2, 5.3, 5.4, 5.5, 5.6, 5.7, 5.8, 5.9, 6.0, 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7, 6.8, 6.9, 7.0, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 7.8, 7.9, 8.0, 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7, 8.8, 8.9, 9.0, 9.1, 9.2, 9.3, 9.4, 9.5, 9.6, 9.7, 9.8, 9.9, 10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1, 11.2, 11.3, 11.4, 11.5, 11.6, 11.7, 11.8, 11.9, 12.0, 12.1, 12.2, 12.3, 12.4, 12.5, 12.6, 12.7, 12.8, 12.9, 13.0, 13.1, 13.2, 13.3, 13.4, 13.5, 13.6, 13.7, 13.8, 13.9, 14.0, 14.1, 14.2, 14.3, 14.4, 14.5, 14.6, 14.7, 14.8, 14.9, 15.0, 15.1, 15.2, 15.3, 15.4, 15.5, 15.6, 15.7, 15.8, 15.9, 16.0, 16.1, 16.2, 16.3, 16.4, 16.5, 16.6, 16.7, 16.8, 16.9, 17.0, 17.1, 17.2, 17.3, 17.4, 17.5, 17.6, 17.7, 17.8, 17.9, 18.0, 18.1, 18.2, 18.3, 18.4, 18.5, 18.6, 18.7, 18.8, 18.9, 19.0, 19.1, 19.2, 19.3, 19.4, 19.5, 19.6, 19.7, 19.8, 19.9, 20.0, 20.1, 20.2, 20.3, 20.4, 20.5, 20.6, 20.7, 20.8, 20.9, 21.0, 21.1, 21.2, 21.3, 21.4, 21.5, 21.6, 21.7, 21.8, 21.9, 22.0, 22.1, 22.2, 22.3, 22.4, 22.5, 22.6, 22.7, 22.8, 22.9, 23.0, 23.1, 23.2, 23.3, 23.4, 23.5, 23.6, 23.7, 23.8, 23.9, 24.0, 24.1, 24.2, 24.3, 24.4, 24.5, 24.6, 24.7, 24.8, 24.9, 25.0, 25.1, 25.2, 25.3, 25.4, 25.5, 25.6, 25.7, 25.8, 25.9, 26.0, 26.1, 26.2, 26.3, 26.4, 26.5, 26.6, 26.7, 26.8, 26.9, 27.0, 27.1, 27.2, 27.3, 27.4, 27.5, 27.6, 27.7, 27.8, 27.9, 28.0, 28.1, 28.2, 28.3, 28.4, 28.5, 28.6, 28.7, 28.8, 28.9, 29.0, 29.1, 29.2, 29.3, 29.4, 29.5, 29.6, 29.7, 29.8, 29.9, 30.0, 30.1, 30.2, 30.3, 30.4, 30.5, 30.6, 30.7, 30.8, 30.9, 31.0, 31.1, 31.2, 31.3, 31.4, 31.5, 31.6, 31.7, 31.8, 31.9, 32.0, 32.1, 32.2, 32.3, 32.4, 32.5, 32.6, 32.7, 32.8, 32.9, 33.0, 33.1, 33.2, 33.3, 33.4, 33.5, 33.6, 33.7, 33.8, 33.9, 34.0, 34.1, 34.2, 34.3, 34.4, 34.5, 34.6, 34.7, 34.8, 34.9, 35.0, 35.1, 35.2, 35.3, 35.4, 35.5, 35.6, 35.7, 35.8, 35.9, 36.0, 36.1, 36.2, 36.3, 36.4, 36.5, 36.6, 36.7, 36.8, 36.9, 37.0, 37.1, 37.2, 37.3, 37.4, 37.5, 37.6, 37.7, 37.8, 37.9, 38.0, 38.1, 38.2, 38.3, 38.4, 38.5, 38.6, 38.7, 38.8, 38.9, 39.0, 39.1, 39.2, 39.3, 39.4, 39.5, 39.6, 39.7, 39.8, 39.9, 40.0, 40.1, 40.2, 40.3, 40.4, 40.5, 40.6, 40.7, 40.8, 40.9, 41.0, 41.1, 41.2, 41.3, 41.4, 41.5, 41.6, 41.7, 41.8, 41.9, 42.0, 42.1, 42.2, 42.3, 42.4, 42.5, 42.6, 42.7, 42.8, 42.9, 43.0, 43.1, 43.2, 43.3, 43.4, 43.5, 43.6, 43.7, 43.8, 43.9, 44.0, 44.1, 44.2, 44.3, 44.4, 44.5, 44.6, 44.7, 44.8, 44.9, 45.0, 45.1, 45.2, 45.3, 45.4, 45.5, 45.6]}, "K2": {"count": 16, "percentage": 3.5, "depth_distribution": [0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1]}}}, "gz_analysis_results": {"final_assessment": "III类桩\n\n分析配置:\n- K值计算深度范围: 已启用 (0.5m / 50cm)\n- 启用指标: 声速, 波幅, 能量\n- 深度分析范围: 全部范围 (默认)\n- 判定依据: 新版GZ Traditio", "k_value_distribution_statistics": {"K1": {"count": 435, "percentage": 96.5}, "K2": {"count": 16, "percentage": 3.5}, "summary": {"total_measurement_points": 451, "k_value_type_count": 2, "max_k_value": 2, "min_k_value": 1}}, "depth_k_value_mapping": {"0.6": 2, "0.7": 2, "0.8": 2, "0.9": 2, "1.0": 2, "1.1": 2, "1.2": 2, "1.3": 2, "1.4": 2, "1.5": 2, "1.6": 2, "1.7": 2, "1.8": 2, "1.9": 2, "2.0": 2, "2.1": 2, "2.2": 1, "2.3": 1, "2.4": 1, "2.5": 1, "2.6": 1, "2.7": 1, "2.8": 1, "2.9": 1, "3.0": 1, "3.1": 1, "3.2": 1, "3.3": 1, "3.4": 1, "3.5": 1, "3.6": 1, "3.7": 1, "3.8": 1, "3.9": 1, "4.0": 1, "4.1": 1, "4.2": 1, "4.3": 1, "4.4": 1, "4.5": 1, "4.6": 1, "4.7": 1, "4.8": 1, "4.9": 1, "5.0": 1, "5.1": 1, "5.2": 1, "5.3": 1, "5.4": 1, "5.5": 1, "5.6": 1, "5.7": 1, "5.8": 1, "5.9": 1, "6.0": 1, "6.1": 1, "6.2": 1, "6.3": 1, "6.4": 1, "6.5": 1, "6.6": 1, "6.7": 1, "6.8": 1, "6.9": 1, "7.0": 1, "7.1": 1, "7.2": 1, "7.3": 1, "7.4": 1, "7.5": 1, "7.6": 1, "7.7": 1, "7.8": 1, "7.9": 1, "8.0": 1, "8.1": 1, "8.2": 1, "8.3": 1, "8.4": 1, "8.5": 1, "8.6": 1, "8.7": 1, "8.8": 1, "8.9": 1, "9.0": 1, "9.1": 1, "9.2": 1, "9.3": 1, "9.4": 1, "9.5": 1, "9.6": 1, "9.7": 1, "9.8": 1, "9.9": 1, "10.0": 1, "10.1": 1, "10.2": 1, "10.3": 1, "10.4": 1, "10.5": 1, "10.6": 1, "10.7": 1, "10.8": 1, "10.9": 1, "11.0": 1, "11.1": 1, "11.2": 1, "11.3": 1, "11.4": 1, "11.5": 1, "11.6": 1, "11.7": 1, "11.8": 1, "11.9": 1, "12.0": 1, "12.1": 1, "12.2": 1, "12.3": 1, "12.4": 1, "12.5": 1, "12.6": 1, "12.7": 1, "12.8": 1, "12.9": 1, "13.0": 1, "13.1": 1, "13.2": 1, "13.3": 1, "13.4": 1, "13.5": 1, "13.6": 1, "13.7": 1, "13.8": 1, "13.9": 1, "14.0": 1, "14.1": 1, "14.2": 1, "14.3": 1, "14.4": 1, "14.5": 1, "14.6": 1, "14.7": 1, "14.8": 1, "14.9": 1, "15.0": 1, "15.1": 1, "15.2": 1, "15.3": 1, "15.4": 1, "15.5": 1, "15.6": 1, "15.7": 1, "15.8": 1, "15.9": 1, "16.0": 1, "16.1": 1, "16.2": 1, "16.3": 1, "16.4": 1, "16.5": 1, "16.6": 1, "16.7": 1, "16.8": 1, "16.9": 1, "17.0": 1, "17.1": 1, "17.2": 1, "17.3": 1, "17.4": 1, "17.5": 1, "17.6": 1, "17.7": 1, "17.8": 1, "17.9": 1, "18.0": 1, "18.1": 1, "18.2": 1, "18.3": 1, "18.4": 1, "18.5": 1, "18.6": 1, "18.7": 1, "18.8": 1, "18.9": 1, "19.0": 1, "19.1": 1, "19.2": 1, "19.3": 1, "19.4": 1, "19.5": 1, "19.6": 1, "19.7": 1, "19.8": 1, "19.9": 1, "20.0": 1, "20.1": 1, "20.2": 1, "20.3": 1, "20.4": 1, "20.5": 1, "20.6": 1, "20.7": 1, "20.8": 1, "20.9": 1, "21.0": 1, "21.1": 1, "21.2": 1, "21.3": 1, "21.4": 1, "21.5": 1, "21.6": 1, "21.7": 1, "21.8": 1, "21.9": 1, "22.0": 1, "22.1": 1, "22.2": 1, "22.3": 1, "22.4": 1, "22.5": 1, "22.6": 1, "22.7": 1, "22.8": 1, "22.9": 1, "23.0": 1, "23.1": 1, "23.2": 1, "23.3": 1, "23.4": 1, "23.5": 1, "23.6": 1, "23.7": 1, "23.8": 1, "23.9": 1, "24.0": 1, "24.1": 1, "24.2": 1, "24.3": 1, "24.4": 1, "24.5": 1, "24.6": 1, "24.7": 1, "24.8": 1, "24.9": 1, "25.0": 1, "25.1": 1, "25.2": 1, "25.3": 1, "25.4": 1, "25.5": 1, "25.6": 1, "25.7": 1, "25.8": 1, "25.9": 1, "26.0": 1, "26.1": 1, "26.2": 1, "26.3": 1, "26.4": 1, "26.5": 1, "26.6": 1, "26.7": 1, "26.8": 1, "26.9": 1, "27.0": 1, "27.1": 1, "27.2": 1, "27.3": 1, "27.4": 1, "27.5": 1, "27.6": 1, "27.7": 1, "27.8": 1, "27.9": 1, "28.0": 1, "28.1": 1, "28.2": 1, "28.3": 1, "28.4": 1, "28.5": 1, "28.6": 1, "28.7": 1, "28.8": 1, "28.9": 1, "29.0": 1, "29.1": 1, "29.2": 1, "29.3": 1, "29.4": 1, "29.5": 1, "29.6": 1, "29.7": 1, "29.8": 1, "29.9": 1, "30.0": 1, "30.1": 1, "30.2": 1, "30.3": 1, "30.4": 1, "30.5": 1, "30.6": 1, "30.7": 1, "30.8": 1, "30.9": 1, "31.0": 1, "31.1": 1, "31.2": 1, "31.3": 1, "31.4": 1, "31.5": 1, "31.6": 1, "31.7": 1, "31.8": 1, "31.9": 1, "32.0": 1, "32.1": 1, "32.2": 1, "32.3": 1, "32.4": 1, "32.5": 1, "32.6": 1, "32.7": 1, "32.8": 1, "32.9": 1, "33.0": 1, "33.1": 1, "33.2": 1, "33.3": 1, "33.4": 1, "33.5": 1, "33.6": 1, "33.7": 1, "33.8": 1, "33.9": 1, "34.0": 1, "34.1": 1, "34.2": 1, "34.3": 1, "34.4": 1, "34.5": 1, "34.6": 1, "34.7": 1, "34.8": 1, "34.9": 1, "35.0": 1, "35.1": 1, "35.2": 1, "35.3": 1, "35.4": 1, "35.5": 1, "35.6": 1, "35.7": 1, "35.8": 1, "35.9": 1, "36.0": 1, "36.1": 1, "36.2": 1, "36.3": 1, "36.4": 1, "36.5": 1, "36.6": 1, "36.7": 1, "36.8": 1, "36.9": 1, "37.0": 1, "37.1": 1, "37.2": 1, "37.3": 1, "37.4": 1, "37.5": 1, "37.6": 1, "37.7": 1, "37.8": 1, "37.9": 1, "38.0": 1, "38.1": 1, "38.2": 1, "38.3": 1, "38.4": 1, "38.5": 1, "38.6": 1, "38.7": 1, "38.8": 1, "38.9": 1, "39.0": 1, "39.1": 1, "39.2": 1, "39.3": 1, "39.4": 1, "39.5": 1, "39.6": 1, "39.7": 1, "39.8": 1, "39.9": 1, "40.0": 1, "40.1": 1, "40.2": 1, "40.3": 1, "40.4": 1, "40.5": 1, "40.6": 1, "40.7": 1, "40.8": 1, "40.9": 1, "41.0": 1, "41.1": 1, "41.2": 1, "41.3": 1, "41.4": 1, "41.5": 1, "41.6": 1, "41.7": 1, "41.8": 1, "41.9": 1, "42.0": 1, "42.1": 1, "42.2": 1, "42.3": 1, "42.4": 1, "42.5": 1, "42.6": 1, "42.7": 1, "42.8": 1, "42.9": 1, "43.0": 1, "43.1": 1, "43.2": 1, "43.3": 1, "43.4": 1, "43.5": 1, "43.6": 1, "43.7": 1, "43.8": 1, "43.9": 1, "44.0": 1, "44.1": 1, "44.2": 1, "44.3": 1, "44.4": 1, "44.5": 1, "44.6": 1, "44.7": 1, "44.8": 1, "44.9": 1, "45.0": 1, "45.1": 1, "45.2": 1, "45.3": 1, "45.4": 1, "45.5": 1, "45.6": 1}, "max_k_depth": 0.6, "max_k_value": 2, "critical_depth_assessment": {"depth": 0.6, "k_value": 2, "profile_assessments": {}, "calculation_process": "公式: K(i) = int[(∑I(j,i)² / ∑I(j,i)) + 0.5]\n    I(j,i)值: [2, 1, 2]\n    ∑I(j,i)² = 2² + 1² + 2² = 4 + 1 + 4 = 9 (排除15个N/A值)\n    ∑I(j,i) = 2 + 1 + 2 = 5 (排除15个N/A值)\n    计算: (9 / 5) + 0.5 = 1.80 + 0.5 = 2.30\n    取整: int(2.30) = 2", "total_profiles": 0}, "iji_value_details": {}, "raw_parameter_data": {}, "continuous_k_range_analysis": [{"start_depth": 0.6, "end_depth": 2.1, "k_value": 2, "range_length_m": 1.5, "range_length_cm": 150, "depth_points": [0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1], "point_count": 16}], "assessment_criteria": ["K值分布统计:", "K=1: 435个截面 (96.5%)", "K=2: 16个截面 (3.5%)", "总计分析截面: 451个", "最大K值位置: 深度0.60m处，K值=2", "相同最大K值出现位置: 0.60m, 0.70m, 0.80m, 0.90m, 1.00m, 1.10m, 1.20m, 1.30m, 1.40m, 1.50m, 1.60m, 1.70m, 1.80m, 1.90m, 2.00m, 2.10m", "* 连续范围1：深度 0.6m - 2.1m（连续长度：1.5m）", "* 涉及深度：0.6m, 0.7m, 0.8m, 0.9m, 1.0m, 1.1m, 1.2m, 1.3m, 1.4m, 1.5m, 1.6m, 1.7m, 1.8m, 1.9m, 2.0m, 2.1m"], "analysis_configuration": ["K值计算深度范围: 已启用 (0.5m / 50cm)", "启用指标: 声速, 波幅, 能量", "深度分析范围: 全部范围 (默认)", "判定依据: 新版GZ Traditional Analysis分类规则"]}, "source_file": "pile_integrity_analysis_20250816_092421.txt", "data_version": "v2.0_enhanced", "export_info": {"export_time": "2025-08-16T09:24:22.066474", "export_version": "v2.0_enhanced", "system_info": {"analysis_engine": "GZ Traditional Analysis Method", "data_processing_version": "2025 Standard", "json_format_version": "2.0"}}}