#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Automated Pile Foundation Data Analysis Script with Batch Processing
Functions:
1. Accept pile foundation ID parameter OR batch process all TXT files
2. Automatically discover and process all .TXT files in directory (batch mode)
3. Execute 3-stage processing pipeline: connection_data.py → process_data.py → pile_analysis_processor.py
4. Organize files: move *_recreated.txt to raw/, *_processed.txt to processed/
5. Comprehensive error handling and logging for batch operations
6. Extract pile foundation integrity assessment results
7. Export results in JSON format
8. Clean up files generated during execution
"""

import os
import sys
import json
import glob
import shutil
import argparse
import subprocess
import re
from datetime import datetime
from pathlib import Path
import requests
import logging
from typing import List, Dict, Tuple

# Import unified core modules
try:
    from pile_analysis_core import file_manager, logger
    CORE_AVAILABLE = True
except ImportError:
    logger = None
    file_manager = None
    CORE_AVAILABLE = False
    print("[WARNING] pile_analysis_core module unavailable, using traditional file management")


class AutomatedPileAnalysis:
    def __init__(self, pile_id=None, keep_files=False, batch_mode=False):
        self.pile_id = pile_id
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.original_files = set()
        self.generated_files = set()
        self.results = {}
        self.keep_files = keep_files
        self.batch_mode = batch_mode

        # Batch processing attributes
        self.batch_results = {}
        self.batch_summary = {
            'total_files': 0,
            'successful': 0,
            'failed': 0,
            'errors': []
        }

        # Setup logging for batch operations
        self.setup_batch_logging()

        # Ensure required directories exist
        self.ensure_directories()

        # Initialize translation mapping
        self.initialize_translation_map()

    def setup_batch_logging(self):
        """Setup logging for batch processing operations"""
        log_file = os.path.join(self.script_dir, f'batch_processing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )

        self.logger = logging.getLogger(__name__)
        self.log_file = log_file

        if self.batch_mode:
            self.logger.info("=== BATCH PROCESSING MODE INITIALIZED ===")
            self.logger.info(f"Log file: {os.path.basename(log_file)}")

    def ensure_directories(self):
        """Ensure required directories exist for file organization"""
        directories = ['raw', 'processed']

        for dir_name in directories:
            dir_path = os.path.join(self.script_dir, dir_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                if self.batch_mode:
                    self.logger.info(f"Created directory: {dir_name}/")
            else:
                if self.batch_mode:
                    self.logger.info(f"Directory exists: {dir_name}/")

    def discover_txt_files(self) -> List[str]:
        """Discover only original .TXT files in the current directory for batch processing"""
        txt_files = []

        # Search for .TXT files (case insensitive)
        for pattern in ['*.TXT', '*.txt']:
            files = glob.glob(os.path.join(self.script_dir, pattern))
            for file_path in files:
                filename = os.path.basename(file_path)
                # Only include original TXT files, exclude generated files
                if not self.is_generated_file(filename):
                    txt_files.append(filename)

        # Remove duplicates and sort
        txt_files = sorted(list(set(txt_files)))

        if self.batch_mode:
            self.logger.info(f"Discovered {len(txt_files)} original TXT files for batch processing:")
            for i, file in enumerate(txt_files, 1):
                self.logger.info(f"  {i}. {file}")

        return txt_files

    def is_generated_file(self, filename: str) -> bool:
        """Check if a file is a generated file (should be excluded from batch processing)"""
        # Exclude files with these patterns (generated files)
        generated_patterns = [
            '_recreated.txt',
            '_processed.txt',
            '_recreated.TXT',
            '_processed.TXT'
        ]

        filename_lower = filename.lower()
        for pattern in generated_patterns:
            if pattern.lower() in filename_lower:
                return True

        return False

    def extract_pile_id_from_filename(self, filename: str) -> str:
        """Extract pile ID from TXT filename"""
        # Remove .TXT extension (case insensitive)
        pile_id = re.sub(r'\.txt$', '', filename, flags=re.IGNORECASE)
        return pile_id.strip()

    def organize_generated_files_immediately(self, pile_id: str, stage: str):
        """Immediately organize generated files after each processing stage"""
        try:
            files_moved = 0

            if stage == "stage1":
                # Move *_recreated.txt files to raw/ directory immediately after stage 1
                recreated_pattern = f"{pile_id}_recreated.txt"
                recreated_file = os.path.join(self.script_dir, recreated_pattern)
                if os.path.exists(recreated_file):
                    target_path = os.path.join(self.script_dir, 'raw', recreated_pattern)
                    shutil.move(recreated_file, target_path)
                    self.logger.info(f"[STAGE1] Moved {recreated_pattern} to raw/")
                    files_moved += 1

                # Also move any *_recreated.xlsx files to raw/
                recreated_xlsx_pattern = f"{pile_id}_recreated.xlsx"
                recreated_xlsx_file = os.path.join(self.script_dir, recreated_xlsx_pattern)
                if os.path.exists(recreated_xlsx_file):
                    target_path = os.path.join(self.script_dir, 'raw', recreated_xlsx_pattern)
                    shutil.move(recreated_xlsx_file, target_path)
                    self.logger.info(f"[STAGE1] Moved {recreated_xlsx_pattern} to raw/")
                    files_moved += 1

            elif stage == "stage2":
                # Move *_processed.txt files to processed/ directory immediately after stage 2
                processed_pattern = f"{pile_id}_processed.txt"
                processed_file = os.path.join(self.script_dir, processed_pattern)
                if os.path.exists(processed_file):
                    target_path = os.path.join(self.script_dir, 'processed', processed_pattern)
                    shutil.move(processed_file, target_path)
                    self.logger.info(f"[STAGE2] Moved {processed_pattern} to processed/")
                    files_moved += 1

            return files_moved

        except Exception as e:
            self.logger.error(f"Error organizing files for {pile_id} at {stage}: {str(e)}")
            return 0

    def run_script_with_file_path(self, script_name: str, description: str, pile_id: str, input_dir: str) -> bool:
        """Run script with files located in specific directory (raw/ or processed/)"""
        try:
            if self.batch_mode:
                self.logger.info(f"Executing: {description} (reading from {input_dir}/)")
            else:
                print(f"[执行] {description} (从 {input_dir}/ 读取文件)...")

            script_path = os.path.join(self.script_dir, script_name)

            if not os.path.exists(script_path):
                if self.batch_mode:
                    self.logger.error(f"Script not found: {script_path}")
                else:
                    print(f"[错误] 脚本不存在: {script_path}")
                return False

            # For process_data.py, we need to temporarily copy the file back to current directory
            # because the script expects the file in the current directory
            if script_name == 'process_data.py' and input_dir == 'raw':
                recreated_file = f"{pile_id}_recreated.txt"
                source_path = os.path.join(self.script_dir, input_dir, recreated_file)
                temp_path = os.path.join(self.script_dir, recreated_file)

                if os.path.exists(source_path):
                    shutil.copy2(source_path, temp_path)
                    self.logger.info(f"Temporarily copied {recreated_file} from {input_dir}/ to current directory")

                    # Run the script normally
                    success = self.run_script(script_name, description, [pile_id])

                    # Clean up temporary file
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                        self.logger.info(f"Cleaned up temporary file: {recreated_file}")

                    return success
                else:
                    self.logger.error(f"Source file not found: {source_path}")
                    return False

            # For pile_analysis_processor.py, we need to temporarily copy the processed file
            elif script_name == 'pile_analysis_processor.py' and input_dir == 'processed':
                processed_file = f"{pile_id}_processed.txt"
                source_path = os.path.join(self.script_dir, input_dir, processed_file)
                temp_path = os.path.join(self.script_dir, processed_file)

                if os.path.exists(source_path):
                    shutil.copy2(source_path, temp_path)
                    self.logger.info(f"Temporarily copied {processed_file} from {input_dir}/ to current directory")

                    # Run the script normally
                    success = self.run_script(script_name, description)

                    # Clean up temporary file
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                        self.logger.info(f"Cleaned up temporary file: {processed_file}")

                    return success
                else:
                    self.logger.error(f"Source file not found: {source_path}")
                    return False

            else:
                # For other scripts, run normally
                return self.run_script(script_name, description, [pile_id])

        except Exception as e:
            error_msg = str(e).encode('ascii', 'ignore').decode('ascii')
            if self.batch_mode:
                self.logger.error(f"Exception in run_script_with_file_path: {error_msg}")
            else:
                print(f"[异常] 运行脚本时发生异常: {error_msg}")
            return False

    def initialize_translation_map(self):
        """Initialize the comprehensive Chinese to English translation mapping"""
        self.translation_map = {
            # Top-level keys
            "桩身编号": "pile_id",
            "分析时间戳": "analysis_timestamp",
            "分析方法": "analysis_method",
            "桩基完整性类别": "pile_integrity_category",
            "桩基完整性类别类型": "pile_integrity_category_type",
            "统计摘要": "statistical_summary",
            "GZ方法分析结果": "gz_analysis_results",
            "源文件": "source_file",
            "数据版本": "data_version",
            "导出信息": "export_info",

            # Statistical summary keys
            "总测点数": "total_measurement_points",
            "分析深度范围": "analysis_depth_range",
            "最小深度": "min_depth",
            "最大深度": "max_depth",
            "深度间隔数": "depth_interval_count",
            "K值分布详情": "k_value_distribution_details",
            "I(j,i)值分布": "iji_value_distribution",
            "剖面总数": "total_profiles",

            # GZ analysis results keys
            "最终判定": "final_assessment",
            "K值分布统计": "k_value_distribution_statistics",
            "深度K值映射": "depth_k_value_mapping",
            "最大K值深度": "max_k_depth",
            "最大K值": "max_k_value",
            "关键深度评估": "critical_depth_assessment",
            "I(j,i)值详情": "iji_value_details",
            "原始参数数据": "raw_parameter_data",
            "连续K值深度范围分析": "continuous_k_range_analysis",
            "判定依据": "assessment_criteria",
            "分析配置": "analysis_configuration",

            # K-value distribution keys
            "汇总": "summary",
            "K值类型数": "k_value_type_count",
            "最高K值": "max_k_value",
            "最低K值": "min_k_value",

            # Nested statistical keys
            "数量": "count",
            "百分比": "percentage",
            "深度分布": "depth_distribution",

            # Continuous K range analysis keys
            "起始深度": "start_depth",
            "结束深度": "end_depth",
            "K值": "k_value",
            "范围长度": "range_length_m",
            "范围长度厘米": "range_length_cm",
            "深度点列表": "depth_points",
            "深度点数量": "point_count",

            # Critical depth assessment keys
            "深度": "depth",
            "剖面评估": "profile_assessments",
            "计算过程": "calculation_process",
            "剖面总数": "total_profiles",
            "评估原因": "assessment_reason",

            # Export info keys
            "导出时间": "export_time",
            "导出版本": "export_version",
            "系统信息": "system_info",
            "分析引擎": "analysis_engine",
            "数据处理版本": "data_processing_version",
            "JSON格式版本": "json_format_version",

            # Common values
            "GZ传统分析法": "GZ Traditional Analysis Method",
            "2025标准": "2025 Standard",
            "未知": "Unknown",
            "I类桩": "Class I Pile",
            "II类桩": "Class II Pile",
            "III类桩": "Class III Pile",
            "IV类桩": "Class IV Pile"
        }

    def translate_dict_keys(self, data):
        """
        Recursively translate Chinese keys to English in dictionaries and lists

        Args:
            data: Dictionary, list, or other data structure to translate

        Returns:
            Translated data structure with English keys
        """
        if isinstance(data, dict):
            translated = {}
            for key, value in data.items():
                # Translate the key if it exists in the mapping
                english_key = self.translation_map.get(key, key)
                # Recursively translate the value
                translated[english_key] = self.translate_dict_keys(value)
            return translated
        elif isinstance(data, list):
            # Recursively translate each item in the list
            return [self.translate_dict_keys(item) for item in data]
        elif isinstance(data, str):
            # Translate string values if they exist in the mapping
            return self.translation_map.get(data, data)
        else:
            # Return other data types as-is
            return data

    def record_original_files(self):
        """Record file list before execution"""
        print(f"[RECORD] Recording file status before execution...")
        for pattern in ['*.txt', '*.xlsx']:
            for file_path in glob.glob(os.path.join(self.script_dir, pattern)):
                self.original_files.add(os.path.basename(file_path))
        print(f"[RECORD] Original files: {len(self.original_files)} files")
        
    def backup_connection_data(self):
        """备份connection_data.py文件"""
        source = os.path.join(self.script_dir, 'connection_data.py')
        backup = os.path.join(self.script_dir, 'connection_data_backup.py')
        shutil.copy2(source, backup)
        print(f"[备份] 已备份connection_data.py")
        
    def modify_pile_id(self):
        """修改connection_data.py中的pile_id"""
        file_path = os.path.join(self.script_dir, 'connection_data.py')
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换pile_id
        pattern = r'pile_id\s*=\s*["\'][^"\']*["\']'
        replacement = f'pile_id = "{self.pile_id}"'
        
        if re.search(pattern, content):
            new_content = re.sub(pattern, replacement, content)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"[修改] 已将pile_id修改为: {self.pile_id}")
            return True
        else:
            print(f"[错误] 未找到pile_id设置行")
            return False
    
    def restore_connection_data(self):
        """恢复connection_data.py文件"""
        source = os.path.join(self.script_dir, 'connection_data_backup.py')
        target = os.path.join(self.script_dir, 'connection_data.py')
        
        if os.path.exists(source):
            shutil.copy2(source, target)
            os.remove(source)
            print(f"[恢复] 已恢复connection_data.py原始状态")
        
    def run_script(self, script_name, description, args=None):
        """运行指定的Python脚本，支持robust Unicode编码处理"""
        script_path = os.path.join(self.script_dir, script_name)

        if not os.path.exists(script_path):
            if self.batch_mode:
                self.logger.error(f"Script not found: {script_path}")
            else:
                print(f"[错误] 脚本不存在: {script_path}")
            return False

        if self.batch_mode:
            self.logger.info(f"Running: {description}")
            self.logger.info(f"Script path: {script_path}")
        else:
            print(f"[运行] {description}...")
            print(f"[调试] 脚本路径: {script_path}")

        try:
            # 切换到脚本目录
            original_cwd = os.getcwd()
            os.chdir(self.script_dir)

            # 设置UTF-8环境变量，确保子进程输出UTF-8编码
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            env['PYTHONLEGACYWINDOWSSTDIO'] = '0'  # Force UTF-8 on Windows

            # 准备命令参数
            cmd_args = [sys.executable, script_name]
            if args:
                cmd_args.extend(args)
                print(f"[调试] 命令参数: {args}")

            print(f"[调试] 执行命令: {' '.join(cmd_args)}")

            # 编码回退策略：尝试多种编码以处理混合编码环境
            encodings_to_try = ['utf-8', 'gbk', 'cp1252', 'latin1']
            result = None

            for encoding in encodings_to_try:
                try:
                    # 特殊处理桩身分析脚本的交互式输入 - 自动使用全部深度范围
                    if script_name == 'pile_analysis_processor.py':
                        # 自动传递空输入以使用全部深度范围（无需用户交互）
                        user_input = ""

                        # 运行脚本并传递空输入（等同于用户直接按回车）
                        result = subprocess.run(cmd_args,
                                              input=user_input + '\n',
                                              text=True,
                                              encoding=encoding,
                                              errors='replace',  # 替换无效字符而不是失败
                                              capture_output=True,
                                              env=env)
                    else:
                        # 运行其他脚本
                        result = subprocess.run(cmd_args,
                                              capture_output=True,
                                              text=True,
                                              encoding=encoding,
                                              errors='replace',  # 替换无效字符而不是失败
                                              env=env)

                    # 如果成功，跳出循环
                    print(f"[编码] 使用 {encoding} 编码成功")
                    break

                except UnicodeDecodeError as e:
                    print(f"[编码] {encoding} 编码失败: {e}")
                    if encoding == encodings_to_try[-1]:  # 最后一个编码也失败
                        print(f"[编码] 所有编码尝试失败，使用二进制模式作为最后回退")
                        # 使用二进制模式作为最终回退
                        if script_name == 'pile_analysis_processor.py':
                            # 自动传递空输入以使用全部深度范围（无需用户交互）
                            user_input = ""
                            result = subprocess.run(cmd_args,
                                                  input=(user_input + '\n').encode('utf-8'),
                                                  capture_output=True,
                                                  env=env)
                        else:
                            result = subprocess.run(cmd_args, capture_output=True, env=env)

                        # 手动解码输出，使用错误替换
                        if hasattr(result, 'stdout') and result.stdout:
                            result.stdout = result.stdout.decode('utf-8', errors='replace')
                        else:
                            result.stdout = ''
                        if hasattr(result, 'stderr') and result.stderr:
                            result.stderr = result.stderr.decode('utf-8', errors='replace')
                        else:
                            result.stderr = ''
                        break
                    continue

            # 恢复工作目录
            os.chdir(original_cwd)
            
            # 输出脚本执行结果 - 改进的Unicode处理
            print(f"[调试] 脚本退出码: {result.returncode}")

            if result.stdout:
                # Clean Unicode issues in stdout
                clean_stdout = result.stdout.encode('ascii', 'ignore').decode('ascii') if isinstance(result.stdout, str) else str(result.stdout)
                print(f"[脚本输出] {clean_stdout}")
            if result.stderr:
                # Clean Unicode issues in stderr
                clean_stderr = result.stderr.encode('ascii', 'ignore').decode('ascii') if isinstance(result.stderr, str) else str(result.stderr)
                print(f"[脚本错误] {clean_stderr}")

            if result.returncode == 0:
                print(f"[成功] {description}完成")
                return True
            else:
                print(f"[失败] {description}失败，退出码: {result.returncode}")
                return False
                
        except Exception as e:
            # Clean Unicode issues in exception messages
            error_msg = str(e).encode('ascii', 'ignore').decode('ascii')
            print(f"[异常] 运行{description}时发生异常: {error_msg}")
            if self.batch_mode:
                self.logger.error(f"Exception in run_script for {description}: {error_msg}")
            return False
    
    def convert_pile_category_to_type(self, pile_category):
        """Convert pile foundation integrity category text to corresponding numeric type

        Args:
            pile_category (str): Pile foundation integrity category text, such as "Class I Pile", "Class II Pile", etc.

        Returns:
            int: Corresponding numeric value (1-4), returns 0 if unrecognized
        """
        if not pile_category or pile_category == "Unknown" or pile_category == "未知":
            return 0

        # Check in priority order to avoid III being matched by II
        if "IV类桩" in pile_category or "Class IV Pile" in pile_category:
            return 4
        elif "III类桩" in pile_category or "Class III Pile" in pile_category:
            return 3
        elif "II类桩" in pile_category or "Class II Pile" in pile_category:
            return 2
        elif "I类桩" in pile_category or "Class I Pile" in pile_category:
            return 1
        else:
            # Try direct Roman numeral matching (in priority order)
            if re.search(r'\bIV\b', pile_category):
                return 4
            elif re.search(r'\bIII\b', pile_category):
                return 3
            elif re.search(r'\bII\b', pile_category):
                return 2
            elif re.search(r'\bI\b', pile_category):
                return 1
            else:
                return 0

    def extract_analysis_results(self):
        """Extract pile foundation integrity analysis results"""
        print(f"[EXTRACT] Extracting analysis results...")

        # Find the latest analysis result file
        pattern = os.path.join(self.script_dir, 'pile_integrity_analysis_*.txt')
        result_files = glob.glob(pattern)

        if not result_files:
            print(f"[ERROR] Analysis result file not found")
            return False

        # Select the latest file
        latest_file = max(result_files, key=os.path.getctime)
        print(f"[EXTRACT] Analysis result file: {os.path.basename(latest_file)}")

        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取桩基完整性类别
            category_match = re.search(r'桩基完整性类别:\s*([^\\n]+)', content)
            pile_category = category_match.group(1).strip() if category_match else "未知"

            # 转换桩基完整性类别为数字类型
            pile_category_type = self.convert_pile_category_to_type(pile_category)
            
            # 提取GZ方法分析结果的关键信息
            gz_results = {}
            
            # 提取最终判定
            final_match = re.search(r'最终判定:\s*([^\\n]+)', content)
            gz_results['最终判定'] = final_match.group(1).strip() if final_match else "未知"
            
            # 增强的K值分布统计提取
            k_distribution = self._extract_enhanced_k_distribution(content)
            gz_results['K值分布统计'] = k_distribution

            # 提取详细的深度-K值映射
            depth_k_mapping = self._extract_depth_k_mapping(content)
            gz_results['深度K值映射'] = depth_k_mapping

            # 提取关键深度信息（最大K值位置及其评估信息）
            critical_depth_info = self._extract_critical_depth_info(content, depth_k_mapping)
            gz_results['最大K值深度'] = critical_depth_info.get("max_k_depth")
            gz_results['最大K值'] = critical_depth_info.get("max_k_value")
            gz_results['关键深度评估'] = critical_depth_info.get("critical_depth_assessment", {})

            # 提取I(j,i)值详细信息
            iji_details = self._extract_iji_values(content)
            gz_results['I(j,i)值详情'] = iji_details

            # 提取原始参数数据
            raw_parameters = self._extract_raw_parameters(content)
            gz_results['原始参数数据'] = raw_parameters

            # 提取连续K值范围信息（50cm深度范围分析）
            continuous_k_ranges = self._extract_continuous_k_ranges(content, depth_k_mapping, pile_category)
            if continuous_k_ranges:
                gz_results['连续K值深度范围分析'] = continuous_k_ranges

            # 提取增强的判定依据（包含详细的K值分布统计和连续范围信息）
            enhanced_criteria = self._extract_enhanced_assessment_criteria(content, k_distribution, critical_depth_info, continuous_k_ranges, depth_k_mapping)
            gz_results['判定依据'] = enhanced_criteria
            
            # 提取分析配置
            config_section = re.search(r'分析配置:\n(.*?)\n\n', content, re.DOTALL)
            if config_section:
                config_lines = [line.strip('- ').strip() for line in config_section.group(1).split('\n') if line.strip()]
                gz_results['分析配置'] = config_lines
            
            # 生成统计摘要
            statistical_summary = self._generate_statistical_summary(k_distribution, depth_k_mapping, iji_details)

            # 构建增强的最终结果
            self.results = {
                "桩身编号": self.pile_id,
                "分析时间戳": datetime.now().isoformat(),
                "分析方法": "GZ传统分析法",
                "桩基完整性类别": pile_category,
                "桩基完整性类别类型": pile_category_type,
                "统计摘要": statistical_summary,
                "GZ方法分析结果": gz_results,
                "源文件": os.path.basename(latest_file),
                "数据版本": "v2.0_enhanced"
            }
            
            print(f"[成功] 增强结果提取完成")
            print(f"[结果] 桩基完整性类别: {pile_category}")
            print(f"[结果] 最终判定: {gz_results.get('最终判定', '未知')}")
            print(f"[统计] 总测点数: {statistical_summary.get('总测点数', 0)}")
            print(f"[统计] 深度范围: {statistical_summary.get('分析深度范围', {}).get('最小深度', 0):.2f}m - {statistical_summary.get('分析深度范围', {}).get('最大深度', 0):.2f}m")

            # 显示关键深度信息
            if critical_depth_info.get("max_k_depth") is not None:
                print(f"[关键] 最大K值深度: {critical_depth_info.get('max_k_depth'):.2f}m (K={critical_depth_info.get('max_k_value')})")
                critical_assessment = critical_depth_info.get("critical_depth_assessment", {})
                if critical_assessment.get("total_profiles"):
                    print(f"[关键] 该深度剖面数: {critical_assessment.get('total_profiles')}个")
            
            return True

        except Exception as e:
            print(f"[错误] 提取结果时发生异常: {str(e)}")
            return False

    def _extract_enhanced_k_distribution(self, content):
        """提取增强的K值分布统计信息"""
        try:
            k_distribution = {}

            # 基础K值统计 - 修正正则表达式以匹配实际格式
            k_pattern = r'K=(\d+):\s*(\d+)个截面\s*\(([\d.]+)%\)'
            for match in re.finditer(k_pattern, content):
                k_value = int(match.group(1))
                count = int(match.group(2))
                percentage = float(match.group(3))
                k_distribution[f"K{k_value}"] = {
                    "count": count,
                    "percentage": percentage
                }

            # 计算总测点数
            total_points = sum(item["count"] for item in k_distribution.values())

            # 添加汇总信息
            k_distribution["汇总"] = {
                "总测点数": total_points,
                "K值类型数": len(k_distribution),
                "最高K值": max([int(k[1:]) for k in k_distribution.keys() if k != "汇总"]) if k_distribution else 1,
                "最低K值": min([int(k[1:]) for k in k_distribution.keys() if k != "汇总"]) if k_distribution else 1
            }

            return k_distribution

        except Exception as e:
            print(f"[警告] K值分布提取失败: {e}")
            return {}

    def _extract_continuous_k_ranges(self, content, depth_k_mapping, pile_category):
        """
        提取连续K值深度范围信息（50cm范围分析）

        当桩基分类是基于连续K值范围时，提取具体的深度范围信息
        """
        try:
            continuous_ranges = []

            if not depth_k_mapping:
                return continuous_ranges

            # 检查是否存在基于连续K值范围的分类判定
            classification_based_on_continuous = False
            continuous_k_value = None

            # 根据桩基类别和判定依据确定是否基于连续K值范围
            if "IV类桩" in pile_category or "Class IV Pile" in pile_category:
                # IV类桩：检查是否因K=3存在50cm连续范围
                if "K=3存在50cm连续范围" in content:
                    classification_based_on_continuous = True
                    continuous_k_value = 3
            elif "III类桩" in pile_category or "Class III Pile" in pile_category:
                # III类桩：检查是否因K=2存在50cm连续范围
                if "K=2存在50cm连续范围" in content:
                    classification_based_on_continuous = True
                    continuous_k_value = 2

            # 如果分类基于连续K值范围，则分析具体范围
            if classification_based_on_continuous and continuous_k_value:
                continuous_ranges = self._analyze_continuous_k_ranges(depth_k_mapping, continuous_k_value)

            return continuous_ranges

        except Exception as e:
            print(f"[警告] 连续K值范围提取失败: {e}")
            return []

    def _analyze_continuous_k_ranges(self, depth_k_mapping, target_k):
        """
        分析指定K值的连续50cm深度范围

        返回格式: [
            {
                'start_depth': float,      # 起始深度(m)
                'end_depth': float,        # 结束深度(m)
                'k_value': int,            # K值
                'range_length_m': float,   # 范围长度(m)
                'range_length_cm': int,    # 范围长度(cm)
                'depth_points': list,      # 包含的深度点列表
                'point_count': int         # 深度点数量
            },
            ...
        ]
        """
        try:
            continuous_ranges = []

            if not depth_k_mapping:
                return continuous_ranges

            # 获取所有深度并排序
            all_depths = sorted(depth_k_mapping.keys())

            if len(all_depths) < 3:  # 至少需要3个点才能形成范围
                return continuous_ranges

            # 使用更简单直接的算法：寻找所有连续的目标K值序列
            current_sequence = []

            for depth in all_depths:
                if depth_k_mapping[depth] == target_k:
                    # 如果当前深度的K值匹配
                    if not current_sequence:
                        # 开始新序列
                        current_sequence = [depth]
                    else:
                        # 检查是否与前一个深度连续（间距≤0.25m视为连续，考虑浮点数精度）
                        gap = round(depth - current_sequence[-1], 2)
                        if gap <= 0.25:
                            current_sequence.append(depth)
                        else:
                            # 序列中断，检查当前序列是否满足50cm要求
                            self._check_and_add_sequence(current_sequence, target_k, continuous_ranges)
                            # 开始新序列
                            current_sequence = [depth]
                else:
                    # K值不匹配，检查当前序列
                    self._check_and_add_sequence(current_sequence, target_k, continuous_ranges)
                    # 重置序列
                    current_sequence = []

            # 处理最后一个序列
            self._check_and_add_sequence(current_sequence, target_k, continuous_ranges)

            return continuous_ranges

        except Exception as e:
            print(f"[警告] 连续K值范围分析失败: {e}")
            return []

    def _check_and_add_sequence(self, sequence, target_k, continuous_ranges):
        """
        检查序列是否满足连续范围要求，如果满足则添加到结果中

        参数:
        - sequence: 深度序列
        - target_k: 目标K值
        - continuous_ranges: 结果列表
        """
        if len(sequence) >= 3:  # 至少3个点
            sequence_range = sequence[-1] - sequence[0]
            if sequence_range >= 0.5:  # 50cm = 0.5m
                range_info = {
                    'start_depth': sequence[0],
                    'end_depth': sequence[-1],
                    'k_value': target_k,
                    'range_length_m': sequence_range,
                    'range_length_cm': int(sequence_range * 100),
                    'depth_points': sequence.copy(),
                    'point_count': len(sequence)
                }
                continuous_ranges.append(range_info)

    def _extract_critical_depth_info(self, content, depth_k_mapping):
        """提取关键深度信息（最大K值位置及其评估信息）"""
        try:
            if not depth_k_mapping:
                return {}

            # 找到最大K值及其对应的深度
            max_k_value = max(depth_k_mapping.values())
            max_k_depths = [depth for depth, k_val in depth_k_mapping.items() if k_val == max_k_value]

            # 如果有多个相同的最大K值，选择第一个出现的深度
            max_k_depth = min(max_k_depths)

            # 提取该深度的详细评估信息
            critical_assessment = self._extract_depth_assessment_details(content, max_k_depth)

            return {
                "max_k_value": max_k_value,
                "max_k_depth": max_k_depth,
                "critical_depth_assessment": critical_assessment,
                "max_k_depth_count": len(max_k_depths),
                "all_max_k_depths": max_k_depths
            }

        except Exception as e:
            print(f"[警告] 关键深度信息提取失败: {e}")
            return {}

    def _extract_depth_assessment_details(self, content, target_depth):
        """提取指定深度的详细评估信息"""
        try:
            assessment_details = {}

            # 查找详细分析结果部分
            detail_section = re.search(r'详细分析结果:\n-+\n(.*?)(?=\n\n|\Z)', content, re.DOTALL)
            if not detail_section:
                return assessment_details

            detail_content = detail_section.group(1)

            # 构建目标深度的模式
            depth_pattern = rf'深度\s+{target_depth:.2f}m:\s*K\(i\)\s*=\s*(\d+)(.*?)(?=深度\s+[\d.]+m:|$)'
            depth_match = re.search(depth_pattern, detail_content, re.DOTALL)

            if depth_match:
                k_value = int(depth_match.group(1))
                depth_details = depth_match.group(2)

                # 提取剖面I(j,i)值信息
                profile_pattern = r'剖面(\d+-\d+):\s*I\(j,i\)\s*=\s*(\d+)\s*\(([^)]+)\)'
                profiles = {}
                for profile_match in re.finditer(profile_pattern, depth_details):
                    profile_name = profile_match.group(1)
                    iji_value = int(profile_match.group(2))
                    reason = profile_match.group(3)
                    profiles[profile_name] = {
                        "iji_value": iji_value,
                        "assessment_reason": reason
                    }

                # 提取K值计算过程
                calc_pattern = r'K值计算过程:(.*?)(?=深度|$)'
                calc_match = re.search(calc_pattern, depth_details, re.DOTALL)
                calculation_details = calc_match.group(1).strip() if calc_match else ""

                assessment_details = {
                    "depth": target_depth,
                    "k_value": k_value,
                    "profile_assessments": profiles,
                    "calculation_process": calculation_details,
                    "total_profiles": len(profiles)
                }

            return assessment_details

        except Exception as e:
            print(f"[警告] 深度评估详情提取失败: {e}")
            return {}

    def _extract_depth_k_mapping(self, content):
        """提取深度-K值映射关系"""
        try:
            depth_k_mapping = {}

            # 查找详细分析结果部分 - 使用更宽松的模式
            detail_section = re.search(r'详细分析结果:\s*\n-+\s*\n(.*)', content, re.DOTALL)
            if detail_section:
                detail_content = detail_section.group(1)

                # 提取每个深度的K值
                depth_pattern = r'深度\s+([\d.]+)m:\s*K\(i\)\s*=\s*(\d+)'
                for match in re.finditer(depth_pattern, detail_content):
                    depth = float(match.group(1))
                    k_value = int(match.group(2))
                    depth_k_mapping[depth] = k_value

            return depth_k_mapping

        except Exception as e:
            print(f"[警告] 深度K值映射提取失败: {e}")
            import traceback
            traceback.print_exc()
            return {}

    def _extract_iji_values(self, content):
        """提取I(j,i)值详细信息"""
        try:
            iji_details = {}

            # 查找详细分析结果部分
            detail_section = re.search(r'详细分析结果:\n-+\n(.*?)(?=\n\n|\Z)', content, re.DOTALL)
            if detail_section:
                detail_content = detail_section.group(1)

                # 按深度分组提取I(j,i)值
                depth_sections = re.split(r'深度\s+([\d.]+)m:', detail_content)[1:]

                for i in range(0, len(depth_sections), 2):
                    if i + 1 < len(depth_sections):
                        depth = float(depth_sections[i])
                        section_content = depth_sections[i + 1]

                        # 提取该深度下的I(j,i)值
                        iji_pattern = r'剖面(\w+):\s*I\(j,i\)\s*=\s*(\d+)'
                        profiles = {}
                        for match in re.finditer(iji_pattern, section_content):
                            profile = match.group(1)
                            iji_value = int(match.group(2))
                            profiles[profile] = iji_value

                        if profiles:
                            iji_details[depth] = profiles

            return iji_details

        except Exception as e:
            print(f"[警告] I(j,i)值提取失败: {e}")
            return {}

    def _extract_enhanced_assessment_criteria(self, content, k_distribution, critical_depth_info, continuous_k_ranges=None, depth_k_mapping=None):
        """提取增强的判定依据，包含详细的K值分布统计和连续范围信息"""
        try:
            criteria_list = []

            # 添加K值分布统计信息
            criteria_list.append("K值分布统计:")

            # 从k_distribution中提取统计信息
            if k_distribution and "汇总" in k_distribution:
                summary = k_distribution["汇总"]
                total_points = summary.get("总测点数", 0)

                # 添加各K值的统计
                for k_key, k_data in k_distribution.items():
                    if k_key != "汇总" and isinstance(k_data, dict):
                        k_value = k_key[1:]  # 去掉'K'前缀
                        count = k_data.get("count", 0)
                        percentage = k_data.get("percentage", 0)
                        if count > 0:
                            criteria_list.append(f"K={k_value}: {count}个截面 ({percentage:.1f}%)")

                # 添加总计信息
                criteria_list.append(f"总计分析截面: {total_points}个")

            # 添加关键深度信息
            if critical_depth_info:
                max_k_depth = critical_depth_info.get("max_k_depth")
                max_k_value = critical_depth_info.get("max_k_value")
                if max_k_depth is not None and max_k_value is not None:
                    criteria_list.append(f"最大K值位置: 深度{max_k_depth:.2f}m处，K值={max_k_value}")

                    # 如果有多个相同的最大K值位置
                    all_max_depths = critical_depth_info.get("all_max_k_depths", [])
                    if len(all_max_depths) > 1:
                        depth_str = ", ".join([f"{d:.2f}m" for d in all_max_depths])
                        criteria_list.append(f"相同最大K值出现位置: {depth_str}")

            # 提取原有的判定依据
            criteria_section = re.search(r'判定依据:\n(.*?)\n\n', content, re.DOTALL)
            if criteria_section:
                original_criteria = [line.strip('- ').strip() for line in criteria_section.group(1).split('\n') if line.strip()]
                # 处理原有判定依据，增强连续范围信息
                for criterion in original_criteria:
                    if not criterion.startswith("K=") and not criterion.startswith("总计分析截面"):
                        # 检查是否是连续范围相关的判定依据
                        enhanced_criterion = self._enhance_continuous_range_criterion(criterion, continuous_k_ranges, depth_k_mapping)
                        if isinstance(enhanced_criterion, list):
                            criteria_list.extend(enhanced_criterion)
                        else:
                            criteria_list.append(enhanced_criterion)

            return criteria_list

        except Exception as e:
            print(f"[警告] 增强判定依据提取失败: {e}")
            # 回退到原始方法
            criteria_section = re.search(r'判定依据:\n(.*?)\n\n', content, re.DOTALL)
            if criteria_section:
                return [line.strip('- ').strip() for line in criteria_section.group(1).split('\n') if line.strip()]
            return []

    def _enhance_continuous_range_criterion(self, criterion, continuous_k_ranges, depth_k_mapping):
        """
        增强连续范围相关的判定依据，添加详细的深度范围信息

        参数:
        - criterion: 原始判定依据文本
        - continuous_k_ranges: 连续K值范围信息
        - depth_k_mapping: 深度-K值映射

        返回:
        - str 或 list: 增强后的判定依据（单个或多个）
        """
        try:
            # 检查是否是连续范围相关的判定依据
            if "存在50cm连续范围" in criterion or "存在连续范围" in criterion:
                # 提取K值
                k_value_match = re.search(r'K=(\d+)', criterion)
                if k_value_match:
                    target_k = int(k_value_match.group(1))

                    # 如果没有预计算的连续范围信息，现场分析
                    if not continuous_k_ranges and depth_k_mapping:
                        continuous_ranges = self._analyze_continuous_k_ranges(depth_k_mapping, target_k)
                    else:
                        # 从预计算的结果中筛选目标K值的范围
                        continuous_ranges = [r for r in (continuous_k_ranges or []) if r.get('k_value') == target_k]

                    # 筛选出长度≥50cm的连续范围
                    valid_ranges = [r for r in continuous_ranges if r.get('range_length_m', 0) >= 0.5]

                    if valid_ranges:
                        enhanced_criteria = [f"K={target_k}存在50cm连续范围："]

                        # 按起始深度排序
                        valid_ranges.sort(key=lambda x: x['start_depth'])

                        # 添加每个连续范围的详细信息
                        for i, range_info in enumerate(valid_ranges, 1):
                            start_depth = range_info['start_depth']
                            end_depth = range_info['end_depth']
                            range_length = range_info['range_length_m']
                            depth_points = range_info.get('depth_points', [])

                            enhanced_criteria.append(f"  * 连续范围{i}：深度 {start_depth:.1f}m - {end_depth:.1f}m（连续长度：{range_length:.1f}m）")

                        # 添加涉及深度列表
                        all_depths = []
                        for range_info in valid_ranges:
                            all_depths.extend(range_info.get('depth_points', []))

                        # 去重并排序
                        unique_depths = sorted(list(set(all_depths)))
                        if unique_depths:
                            depth_str = ", ".join([f"{d:.1f}m" for d in unique_depths])
                            enhanced_criteria.append(f"  * 涉及深度：{depth_str}")

                        return enhanced_criteria

            # 如果不是连续范围相关的判定依据，直接返回原文
            return criterion

        except Exception as e:
            print(f"[警告] 连续范围判定依据增强失败: {e}")
            return criterion

    def _extract_raw_parameters(self, content):
        """提取原始参数数据（速度、波幅、能量）"""
        try:
            raw_parameters = {}

            # 这里需要根据实际的文本格式来提取原始参数
            # 由于当前文本格式可能不包含详细的原始参数，我们先返回空字典
            # 在实际应用中，可以从processed.txt文件中提取这些信息

            return raw_parameters

        except Exception as e:
            print(f"[警告] 原始参数提取失败: {e}")
            return {}

    def _generate_statistical_summary(self, k_distribution, depth_k_mapping, iji_details):
        """生成统计摘要"""
        try:
            summary = {}

            # 基础统计
            total_points = k_distribution.get("汇总", {}).get("总测点数", 0)
            summary["总测点数"] = total_points
            summary["分析深度范围"] = {
                "最小深度": min(depth_k_mapping.keys()) if depth_k_mapping else 0,
                "最大深度": max(depth_k_mapping.keys()) if depth_k_mapping else 0,
                "深度间隔数": len(depth_k_mapping)
            }

            # K值分布统计
            k_stats = {}
            for k_key, k_data in k_distribution.items():
                if k_key != "汇总" and isinstance(k_data, dict):
                    k_value = int(k_key[1:])
                    k_stats[f"K{k_value}"] = {
                        "数量": k_data.get("count", 0),
                        "百分比": k_data.get("percentage", 0),
                        "深度分布": [depth for depth, k in depth_k_mapping.items() if k == k_value]
                    }

            summary["K值分布详情"] = k_stats

            # I(j,i)值统计
            if iji_details:
                iji_stats = {}
                all_iji_values = []
                for depth, profiles in iji_details.items():
                    all_iji_values.extend(profiles.values())

                for i in range(1, 5):
                    count = all_iji_values.count(i)
                    iji_stats[f"I{i}"] = {
                        "数量": count,
                        "百分比": (count / len(all_iji_values) * 100) if all_iji_values else 0
                    }

                summary["I(j,i)值分布"] = iji_stats
                summary["剖面总数"] = len(all_iji_values)

            return summary

        except Exception as e:
            print(f"[警告] 统计摘要生成失败: {e}")
            return {}

    def export_results_json(self):
        """Export enhanced JSON format results with English keys"""
        if not self.results:
            print(f"[ERROR] No results to export")
            return None

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(self.script_dir, f"pile_analysis_results_{self.pile_id}_{timestamp}.json")

        try:
            # Add export metadata
            enhanced_results = {
                **self.results,
                "导出信息": {
                    "导出时间": datetime.now().isoformat(),
                    "导出版本": "v2.0_enhanced",
                    "系统信息": {
                        "分析引擎": "GZ传统分析法",
                        "数据处理版本": "2025标准",
                        "JSON格式版本": "2.0"
                    }
                }
            }

            # Apply comprehensive translation to all Chinese keys and values
            translated_results = self.translate_dict_keys(enhanced_results)

            # Use better JSON formatting with English keys
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(translated_results, f, ensure_ascii=False, indent=2, sort_keys=False)

            # Output detailed export information
            print(f"[EXPORT] Enhanced JSON results with English keys saved: {os.path.basename(output_file)}")

            # Display statistical information
            stats = self.results.get("统计摘要", {})
            if stats:
                print(f"[STATS] Data included: {stats.get('总测点数', 0)} measurement points, {stats.get('分析深度范围', {}).get('深度间隔数', 0)} depth intervals")
                k_dist = stats.get('K值分布详情', {})
                if k_dist:
                    k_summary = ", ".join([f"{k}:{v.get('数量', 0)} points" for k, v in k_dist.items()])
                    print(f"[STATS] K-value distribution: {k_summary}")

            return output_file

        except Exception as e:
            print(f"[ERROR] Exception occurred during enhanced JSON export: {str(e)}")
            return None
    
    def identify_generated_files(self):
        """识别运行过程中生成的文件"""
        current_files = set()
        for pattern in ['*.txt', '*.xlsx']:
            for file_path in glob.glob(os.path.join(self.script_dir, pattern)):
                current_files.add(os.path.basename(file_path))
        
        self.generated_files = current_files - self.original_files
        print(f"[识别] 新生成文件: {len(self.generated_files)} 个")
        for file in self.generated_files:
            print(f"  - {file}")
    
    def cleanup_generated_files(self):
        """清理运行过程中生成的文件 - 使用优化的文件管理器"""
        if CORE_AVAILABLE and file_manager:
            print(f"[清理] 使用优化文件管理器清理生成的文件...")
            try:
                cleaned_count = file_manager.cleanup_generated_files(
                    self.original_files, self.script_dir
                )
                print(f"[清理] 完成，共删除 {cleaned_count} 个文件")
                return
            except Exception as e:
                print(f"[警告] 优化清理失败，使用传统方法: {e}")

        # 传统清理方法（备用）
        print(f"[清理] 开始清理生成的文件...")
        self.identify_generated_files()

        cleaned_count = 0
        for filename in self.generated_files:
            file_path = os.path.join(self.script_dir, filename)
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"[删除] {filename}")
                    cleaned_count += 1
            except Exception as e:
                print(f"[错误] 删除文件 {filename} 失败: {str(e)}")

        print(f"[清理] 完成，共删除 {cleaned_count} 个文件")

    def cleanup_temporary_files(self):
        """清理临时处理文件 - 删除 *_processed.txt, *_recreated.txt, *_recreated.xlsx"""
        print(f"[临时文件清理] 开始清理临时处理文件...")

        # 定义临时文件模式
        temp_file_patterns = [
            f"{self.pile_id}_processed.txt",
            f"{self.pile_id}_recreated.txt",
            f"{self.pile_id}_recreated.xlsx"
        ]

        cleaned_count = 0
        for pattern in temp_file_patterns:
            file_path = os.path.join(self.script_dir, pattern)
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"[删除] 临时文件: {pattern}")
                    cleaned_count += 1
                else:
                    print(f"[跳过] 文件不存在: {pattern}")
            except Exception as e:
                print(f"[错误] 删除临时文件 {pattern} 失败: {str(e)}")

        print(f"[临时文件清理] 完成，共删除 {cleaned_count} 个临时文件")

        # 显示保留的重要文件
        important_files = []
        for pattern in ['*.json', '*integrity_analysis*.txt', '*.png']:
            for file_path in glob.glob(os.path.join(self.script_dir, pattern)):
                important_files.append(os.path.basename(file_path))

        if important_files:
            print(f"[保留] 重要结果文件:")
            for file in important_files:
                print(f"  - {file}")

    def run_enhanced_sequential_processing(self, pile_id: str) -> bool:
        """Enhanced sequential processing with immediate file organization after each stage and robust error handling"""
        files_moved_stage1 = 0
        files_moved_stage2 = 0
        stage_completed = 0

        try:
            self.logger.info(f"=== ENHANCED SEQUENTIAL PROCESSING: {pile_id} ===")

            # Stage 1: Data extraction using connection_data.py
            self.logger.info(f"[STAGE 1] Data extraction for {pile_id}")
            txt_filename = f"{pile_id}.TXT"

            try:
                if not self.run_script('connection_data.py', f'数据导出 - {pile_id}', ['-f', txt_filename]):
                    self.logger.error(f"[STAGE 1] Script execution failed for {pile_id}")
                    raise Exception(f"Stage 1 script execution failed")

                # Verify Stage 1 output was generated
                recreated_file = f"{pile_id}_recreated.txt"
                if not os.path.exists(os.path.join(self.script_dir, recreated_file)):
                    self.logger.error(f"[STAGE 1] Output missing: {recreated_file}")
                    raise Exception(f"Stage 1 output file missing: {recreated_file}")

                self.logger.info(f"[STAGE 1] SUCCESS: {recreated_file} generated")
                stage_completed = 1

                # IMMEDIATELY organize Stage 1 output
                files_moved_stage1 = self.organize_generated_files_immediately(pile_id, "stage1")
                self.logger.info(f"[STAGE 1] File organization: {files_moved_stage1} files moved to raw/")

            except Exception as stage1_error:
                error_msg = str(stage1_error).encode('ascii', 'ignore').decode('ascii')
                self.logger.error(f"[STAGE 1] FAILED for {pile_id}: {error_msg}")
                raise Exception(f"Stage 1 failed: {error_msg}")

            # Stage 2: Data processing using process_data.py
            self.logger.info(f"[STAGE 2] Data processing for {pile_id}")

            try:
                if not self.run_script_with_file_path('process_data.py', f'数据处理 - {pile_id}', pile_id, 'raw'):
                    self.logger.error(f"[STAGE 2] Script execution failed for {pile_id}")
                    raise Exception(f"Stage 2 script execution failed")

                # Verify Stage 2 output was generated
                processed_file = f"{pile_id}_processed.txt"
                if not os.path.exists(os.path.join(self.script_dir, processed_file)):
                    self.logger.error(f"[STAGE 2] Output missing: {processed_file}")
                    raise Exception(f"Stage 2 output file missing: {processed_file}")

                self.logger.info(f"[STAGE 2] SUCCESS: {processed_file} generated")
                stage_completed = 2

                # IMMEDIATELY organize Stage 2 output
                files_moved_stage2 = self.organize_generated_files_immediately(pile_id, "stage2")
                self.logger.info(f"[STAGE 2] File organization: {files_moved_stage2} files moved to processed/")

            except Exception as stage2_error:
                error_msg = str(stage2_error).encode('ascii', 'ignore').decode('ascii')
                self.logger.error(f"[STAGE 2] FAILED for {pile_id}: {error_msg}")
                raise Exception(f"Stage 2 failed: {error_msg}")

            # Stage 3: Integrity analysis using pile_analysis_processor.py
            self.logger.info(f"[STAGE 3] Integrity analysis for {pile_id}")

            try:
                if not self.run_script_with_file_path('pile_analysis_processor.py', f'桩身完整性分析 - {pile_id}', pile_id, 'processed'):
                    self.logger.warning(f"[STAGE 3] Script execution had issues for {pile_id}, but may have generated output")
                    # Don't fail immediately, check if output was generated

                stage_completed = 3
                self.logger.info(f"[STAGE 3] Completed for {pile_id}")

                # Try to extract analysis results (optional, don't fail if this doesn't work)
                try:
                    if self.extract_analysis_results():
                        self.logger.info(f"[RESULTS] Successfully extracted for {pile_id}")
                    else:
                        self.logger.warning(f"[RESULTS] Could not extract results for {pile_id}, but continuing")
                except Exception as extract_error:
                    error_msg = str(extract_error).encode('ascii', 'ignore').decode('ascii')
                    self.logger.warning(f"[RESULTS] Extraction failed for {pile_id}: {error_msg}")

                # Try to export JSON results (optional, don't fail if this doesn't work)
                try:
                    json_file = self.export_results_json()
                    if json_file:
                        self.logger.info(f"[JSON] Successfully exported for {pile_id}: {os.path.basename(json_file)}")
                    else:
                        self.logger.warning(f"[JSON] Could not export JSON for {pile_id}, but continuing")
                except Exception as json_error:
                    error_msg = str(json_error).encode('ascii', 'ignore').decode('ascii')
                    self.logger.warning(f"[JSON] Export failed for {pile_id}: {error_msg}")

            except Exception as stage3_error:
                error_msg = str(stage3_error).encode('ascii', 'ignore').decode('ascii')
                self.logger.warning(f"[STAGE 3] Had issues for {pile_id}: {error_msg}, but continuing")
                # Don't fail the entire process for Stage 3 issues

            # Store results for batch summary
            if self.batch_mode:
                self.batch_results[pile_id] = {
                    'status': 'success',
                    'stages_completed': stage_completed,
                    'integrity_category': self.results.get('桩基完整性类别', '未知'),
                    'files_moved_stage1': files_moved_stage1,
                    'files_moved_stage2': files_moved_stage2,
                    'total_files_moved': files_moved_stage1 + files_moved_stage2
                }

            self.logger.info(f"=== PILE {pile_id} ENHANCED SEQUENTIAL PROCESSING COMPLETED ({stage_completed}/3 stages) ===")
            return True

        except Exception as e:
            error_msg = str(e).encode('ascii', 'ignore').decode('ascii')  # Clean Unicode issues
            self.logger.error(f"Exception during enhanced sequential analysis of {pile_id}: {error_msg}")
            if self.batch_mode:
                self.batch_results[pile_id] = {
                    'status': 'failed',
                    'stages_completed': stage_completed,
                    'error': error_msg,
                    'files_moved_stage1': files_moved_stage1,
                    'files_moved_stage2': files_moved_stage2,
                    'total_files_moved': files_moved_stage1 + files_moved_stage2
                }
            return False

    def run_batch_analysis(self) -> bool:
        """Run batch analysis for all discovered TXT files"""
        self.logger.info("=== STARTING BATCH ANALYSIS ===")

        # Discover all TXT files
        txt_files = self.discover_txt_files()

        if not txt_files:
            self.logger.warning("No TXT files found for batch processing")
            return False

        self.batch_summary['total_files'] = len(txt_files)

        # Process each file
        for i, txt_file in enumerate(txt_files, 1):
            pile_id = self.extract_pile_id_from_filename(txt_file)

            self.logger.info(f"Processing file {i}/{len(txt_files)}: {txt_file} (Pile ID: {pile_id})")

            # Reset results for each pile
            self.results = {}
            self.pile_id = pile_id

            try:
                # Record original files before processing
                self.record_original_files()

                # Run enhanced sequential processing with immediate file organization
                if self.run_enhanced_sequential_processing(pile_id):
                    self.batch_summary['successful'] += 1
                    self.logger.info(f"SUCCESS: {pile_id} - Enhanced sequential processing completed")
                else:
                    self.batch_summary['failed'] += 1
                    self.batch_summary['errors'].append(f"{pile_id}: Enhanced sequential analysis pipeline failed")
                    self.logger.error(f"FAILED: {pile_id} - Enhanced sequential processing failed")

            except Exception as e:
                self.batch_summary['failed'] += 1
                error_msg = f"{pile_id}: {str(e)}"
                self.batch_summary['errors'].append(error_msg)
                self.logger.error(f"EXCEPTION: {error_msg}")

            finally:
                # Clean up temporary files for this pile
                if not self.keep_files:
                    self.cleanup_temporary_files()

                # Restore connection_data.py if it was modified
                self.restore_connection_data()

        # Generate batch summary report
        self.generate_batch_summary_report()

        return self.batch_summary['successful'] > 0

    def run_analysis(self):
        """运行完整的分析流程 - 支持单个和批量模式"""
        if self.batch_mode:
            return self.run_batch_analysis()
        else:
            # Original single pile analysis
            print(f"=" * 60)
            print(f"自动化桩身数据分析")
            print(f"桩身ID: {self.pile_id}")
            print(f"=" * 60)

            try:
                # 1. 记录原始文件
                self.record_original_files()

                # Run enhanced sequential processing (both batch and single modes use enhanced method)
                success = self.run_enhanced_sequential_processing(self.pile_id)

                if success:
                    # 7. 上传文件到服务器
                    self.upload_files_to_server()

                    print(f"=" * 60)
                    print(f"分析完成！")
                    print(f"桩基完整性类别: {self.results.get('桩基完整性类别', '未知')}")
                    print(f"=" * 60)

                return success

            except Exception as e:
                print(f"[异常] 分析过程中发生异常: {str(e)}")
                return False

            finally:
                # 恢复connection_data.py
                self.restore_connection_data()

    def upload_files_to_server(self):
        """使用connection_data.py中的API接口上传文件到服务器"""
        print(f"[上传] 准备上传文件到服务器...")
        
        # 查找需要上传的文件
        # 排除原始数据文件（processed.txt, recreated.txt, recreated.xlsx），仅上传分析结果和PNG可视化文件
        # 更宽松的JSON文件搜索模式
        json_file = next((f for f in self.generated_files if f.endswith(".json")), None)
        # 更宽松的TXT文件搜索模式
        txt_file = next((f for f in self.generated_files if f.endswith(".txt") and "integrity_analysis" in f), None)
        viz_files = [f for f in self.generated_files if f.endswith(".png")]
        
        # 调试输出查找结果
        print(f"[上传] JSON文件: {json_file if json_file else '未找到'}")
        print(f"[上传] TXT文件: {txt_file if txt_file else '未找到'}")
        print(f"[上传] 可视化文件: {len(viz_files)} 个PNG文件")
        
        # 如果没有找到需要上传的文件，直接返回
        if not json_file or not txt_file or not viz_files:
            print(f"[警告] 缺少需要上传的文件，无法完成上传")
            return False
        
        # 准备上传文件
        files = {}
        
        # 添加JSON文件
        json_path = os.path.join(self.script_dir, json_file)
        if os.path.exists(json_path):
            files['json_result'] = open(json_path, 'rb')
        
        # 添加TXT文件
        txt_path = os.path.join(self.script_dir, txt_file)
        if os.path.exists(txt_path):
            files['txt_report'] = open(txt_path, 'rb')
        
        # 添加PNG可视化文件
        for i, viz_file in enumerate(viz_files):
            viz_path = os.path.join(self.script_dir, viz_file)
            if os.path.exists(viz_path):
                files[f'viz_file_{i}'] = open(viz_path, 'rb')
        
        # 准备表单数据
        data = {'pile_id': self.pile_id}
        
        # 使用connection_data.py中的API接口
        upload_url = "http://*************/pilediag/pile/uploadResults"
        headers = {'Content-Type': 'multipart/form-data'}
        
        try:
            print(f"[上传] 正在上传到: {upload_url}")
            print(f"[上传] 参数: {data}")
            
            # 使用与connection_data.py中相同的请求方式
            response = requests.post(upload_url, data=data, files=files)
            
            # 关闭所有打开的文件
            for file in files.values():
                file.close()
            
            # 检查响应
            if response.status_code == 200:
                print(f"[成功] 文件上传成功")
                print(f"[上传] 服务器响应: {response.text[:200]}")  # 只显示前200个字符
                return True
            else:
                print(f"[失败] 文件上传失败，状态码: {response.status_code}")
                print(f"[上传] 服务器响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"[异常] 文件上传发生错误: {str(e)}")
            # 确保关闭所有打开的文件
            for file in files.values():
                file.close()
            return False

    def generate_batch_summary_report(self):
        """Generate comprehensive batch processing summary report"""
        self.logger.info("=== BATCH PROCESSING SUMMARY REPORT ===")

        # Overall statistics
        total = self.batch_summary['total_files']
        successful = self.batch_summary['successful']
        failed = self.batch_summary['failed']
        success_rate = (successful / total * 100) if total > 0 else 0

        self.logger.info(f"Total files processed: {total}")
        self.logger.info(f"Successful: {successful}")
        self.logger.info(f"Failed: {failed}")
        self.logger.info(f"Success rate: {success_rate:.1f}%")

        # Detailed results
        if self.batch_results:
            self.logger.info("\n=== DETAILED RESULTS ===")
            for pile_id, result in self.batch_results.items():
                if result['status'] == 'success':
                    self.logger.info(f"SUCCESS {pile_id}: {result['integrity_category']} - {result['json_file']}")
                else:
                    self.logger.error(f"FAILED {pile_id}: {result.get('error', 'Unknown error')}")

        # Error summary
        if self.batch_summary['errors']:
            self.logger.info("\n=== ERROR SUMMARY ===")
            for error in self.batch_summary['errors']:
                self.logger.error(f"  - {error}")

        # Generate JSON summary report
        self.export_batch_summary_json()

        self.logger.info("=== BATCH PROCESSING COMPLETED ===")

    def export_batch_summary_json(self):
        """Export batch processing summary to JSON file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_file = os.path.join(self.script_dir, f"batch_summary_{timestamp}.json")

            summary_data = {
                "batch_processing_summary": {
                    "timestamp": datetime.now().isoformat(),
                    "total_files": self.batch_summary['total_files'],
                    "successful": self.batch_summary['successful'],
                    "failed": self.batch_summary['failed'],
                    "success_rate": (self.batch_summary['successful'] / self.batch_summary['total_files'] * 100) if self.batch_summary['total_files'] > 0 else 0,
                    "log_file": os.path.basename(self.log_file)
                },
                "detailed_results": self.batch_results,
                "errors": self.batch_summary['errors']
            }

            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"Batch summary exported: {os.path.basename(summary_file)}")
            return summary_file

        except Exception as e:
            self.logger.error(f"Failed to export batch summary: {str(e)}")
            return None


def main():
    """Enhanced main function with batch processing support"""
    parser = argparse.ArgumentParser(
        description='Enhanced Automated Pile Foundation Data Analysis Script with Batch Processing',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Single pile analysis
  python automated_pile_analysis.py "40001   8-6"

  # Batch process all TXT files in directory
  python automated_pile_analysis.py --batch

  # Batch process with file retention
  python automated_pile_analysis.py --batch --keep-files
        """
    )

    # Mutually exclusive group for single vs batch mode
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument('pile_id', nargs='?', help='桩身ID (for single pile analysis)')
    mode_group.add_argument('--batch', action='store_true', help='批量处理模式 - 自动发现并处理所有TXT文件')

    parser.add_argument('--keep-files', action='store_true', help='保留生成的中间文件')

    args = parser.parse_args()

    try:
        if args.batch:
            # Batch processing mode
            print("=" * 80)
            print("ENHANCED BATCH PROCESSING MODE")
            print("=" * 80)

            analyzer = AutomatedPileAnalysis(
                pile_id=None,
                keep_files=args.keep_files,
                batch_mode=True
            )

            success = analyzer.run_analysis()

            if success:
                print("\n[BATCH SUCCESS] Batch processing completed successfully!")
                print(f"Results: {analyzer.batch_summary['successful']}/{analyzer.batch_summary['total_files']} files processed successfully")
                print(f"Log file: {os.path.basename(analyzer.log_file)}")
                return True
            else:
                print("\n[BATCH ERROR] Batch processing failed!")
                print(f"Results: {analyzer.batch_summary['successful']}/{analyzer.batch_summary['total_files']} files processed successfully")
                return False

        else:
            # Single pile analysis mode
            if not args.pile_id or not args.pile_id.strip():
                print("错误: 请提供有效的桩身ID")
                return False

            print("=" * 80)
            print("SINGLE PILE ANALYSIS MODE")
            print("=" * 80)

            analyzer = AutomatedPileAnalysis(
                pile_id=args.pile_id.strip(),
                keep_files=args.keep_files,
                batch_mode=False
            )

            success = analyzer.run_analysis()

            if success:
                print("\n[SUCCESS] Single pile analysis completed successfully!")
                return True
            else:
                print("\n[ERROR] Single pile analysis failed!")
                return False

    except KeyboardInterrupt:
        print("\n[INTERRUPTED] Processing interrupted by user")
        return False
    except Exception as e:
        print(f"\n[EXCEPTION] Unexpected error: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)