{"pile_id": "10137   244-1", "analysis_timestamp": "2025-08-16T09:24:32.288271", "analysis_method": "GZ Traditional Analysis Method", "pile_integrity_category": "III类桩\n\nGZ方法桩基完整性分析结果\n========================================\n\n最终判定: III类桩\n\n分析配置:\n- K值计算深度范围: 已启用 (0.5m / 50cm)\n- 启用指标: 声速, 波幅, 能量\n- 深度分析范围: 全部范围 (默认)\n- 判定依据: 新版GZ Traditio", "pile_integrity_category_type": 3, "statistical_summary": {"total_measurement_points": 335, "analysis_depth_range": {"min_depth": 0.61, "max_depth": 33.79, "depth_interval_count": 335}, "k_value_distribution_details": {"K1": {"count": 324, "percentage": 96.7, "depth_distribution": [1.49, 1.59, 1.69, 1.79, 1.89, 1.99, 2.09, 2.19, 2.29, 2.39, 2.49, 2.59, 2.69, 2.79, 2.89, 2.99, 3.09, 3.19, 3.29, 3.39, 3.49, 3.59, 3.69, 3.79, 3.89, 3.99, 4.09, 4.19, 4.29, 4.39, 4.49, 4.59, 4.69, 4.79, 4.89, 4.99, 5.09, 5.19, 5.29, 5.39, 5.49, 5.59, 5.69, 5.79, 5.89, 5.99, 6.09, 6.19, 6.29, 6.39, 6.49, 6.59, 6.69, 6.79, 6.89, 6.99, 7.09, 7.19, 7.29, 7.39, 7.49, 7.59, 7.69, 7.79, 7.89, 7.99, 8.09, 8.19, 8.29, 8.39, 8.49, 8.59, 8.69, 8.79, 8.89, 8.99, 9.09, 9.19, 9.29, 9.39, 9.49, 9.59, 9.69, 9.79, 9.89, 9.99, 10.09, 10.19, 10.29, 10.39, 10.49, 10.59, 10.69, 10.79, 10.89, 10.99, 11.09, 11.19, 11.29, 11.39, 11.49, 11.59, 11.69, 11.79, 11.89, 11.99, 12.09, 12.19, 12.29, 12.39, 12.49, 12.59, 12.69, 12.79, 12.89, 12.99, 13.09, 13.19, 13.29, 13.39, 13.49, 13.59, 13.69, 13.79, 13.89, 13.99, 14.09, 14.19, 14.29, 14.39, 14.49, 14.59, 14.69, 14.79, 14.89, 14.99, 15.09, 15.19, 15.29, 15.39, 15.49, 15.59, 15.69, 15.79, 15.89, 15.99, 16.09, 16.19, 16.29, 16.39, 16.49, 16.59, 16.69, 16.79, 16.89, 16.99, 17.09, 17.19, 17.29, 17.39, 17.49, 17.59, 17.69, 17.79, 17.89, 17.99, 18.09, 18.19, 18.29, 18.39, 18.49, 18.59, 18.69, 18.79, 18.89, 18.99, 19.09, 19.19, 19.29, 19.39, 19.49, 19.59, 19.69, 19.79, 19.89, 19.99, 20.09, 20.19, 20.29, 20.39, 20.49, 20.59, 20.69, 20.79, 20.89, 20.99, 21.09, 21.19, 21.29, 21.39, 21.49, 21.59, 21.69, 21.79, 21.89, 21.99, 22.09, 22.19, 22.29, 22.39, 22.49, 22.59, 22.69, 22.79, 22.89, 22.99, 23.09, 23.19, 23.29, 23.39, 23.49, 23.59, 23.69, 23.79, 23.89, 23.99, 24.09, 24.19, 24.29, 24.39, 24.49, 24.59, 24.69, 24.79, 24.89, 24.99, 25.09, 25.19, 25.29, 25.39, 25.49, 25.59, 25.69, 25.79, 25.89, 25.99, 26.09, 26.19, 26.29, 26.39, 26.49, 26.59, 26.69, 26.79, 26.89, 26.99, 27.09, 27.19, 27.29, 27.39, 27.49, 27.59, 27.69, 27.79, 27.89, 27.99, 28.09, 28.19, 28.29, 28.39, 28.49, 28.59, 28.69, 28.79, 28.89, 28.99, 29.09, 29.19, 29.29, 29.39, 29.49, 29.59, 29.69, 29.79, 29.89, 29.99, 30.09, 30.19, 30.29, 30.39, 30.49, 30.59, 30.69, 30.79, 30.89, 30.99, 31.09, 31.19, 31.29, 31.39, 31.49, 31.59, 31.69, 31.79, 31.89, 31.99, 32.09, 32.19, 32.29, 32.39, 32.49, 32.59, 32.69, 32.79, 32.89, 32.99, 33.09, 33.19, 33.29, 33.39, 33.49, 33.59, 33.69, 33.79]}, "K2": {"count": 11, "percentage": 3.3, "depth_distribution": [0.61, 0.69, 0.71, 0.79, 0.81, 0.89, 0.99, 1.09, 1.19, 1.29, 1.39]}}}, "gz_analysis_results": {"final_assessment": "III类桩\n\n分析配置:\n- K值计算深度范围: 已启用 (0.5m / 50cm)\n- 启用指标: 声速, 波幅, 能量\n- 深度分析范围: 全部范围 (默认)\n- 判定依据: 新版GZ Traditio", "k_value_distribution_statistics": {"K1": {"count": 324, "percentage": 96.7}, "K2": {"count": 11, "percentage": 3.3}, "summary": {"total_measurement_points": 335, "k_value_type_count": 2, "max_k_value": 2, "min_k_value": 1}}, "depth_k_value_mapping": {"0.61": 2, "0.69": 2, "0.71": 2, "0.79": 2, "0.81": 2, "0.89": 2, "0.99": 2, "1.09": 2, "1.19": 2, "1.29": 2, "1.39": 2, "1.49": 1, "1.59": 1, "1.69": 1, "1.79": 1, "1.89": 1, "1.99": 1, "2.09": 1, "2.19": 1, "2.29": 1, "2.39": 1, "2.49": 1, "2.59": 1, "2.69": 1, "2.79": 1, "2.89": 1, "2.99": 1, "3.09": 1, "3.19": 1, "3.29": 1, "3.39": 1, "3.49": 1, "3.59": 1, "3.69": 1, "3.79": 1, "3.89": 1, "3.99": 1, "4.09": 1, "4.19": 1, "4.29": 1, "4.39": 1, "4.49": 1, "4.59": 1, "4.69": 1, "4.79": 1, "4.89": 1, "4.99": 1, "5.09": 1, "5.19": 1, "5.29": 1, "5.39": 1, "5.49": 1, "5.59": 1, "5.69": 1, "5.79": 1, "5.89": 1, "5.99": 1, "6.09": 1, "6.19": 1, "6.29": 1, "6.39": 1, "6.49": 1, "6.59": 1, "6.69": 1, "6.79": 1, "6.89": 1, "6.99": 1, "7.09": 1, "7.19": 1, "7.29": 1, "7.39": 1, "7.49": 1, "7.59": 1, "7.69": 1, "7.79": 1, "7.89": 1, "7.99": 1, "8.09": 1, "8.19": 1, "8.29": 1, "8.39": 1, "8.49": 1, "8.59": 1, "8.69": 1, "8.79": 1, "8.89": 1, "8.99": 1, "9.09": 1, "9.19": 1, "9.29": 1, "9.39": 1, "9.49": 1, "9.59": 1, "9.69": 1, "9.79": 1, "9.89": 1, "9.99": 1, "10.09": 1, "10.19": 1, "10.29": 1, "10.39": 1, "10.49": 1, "10.59": 1, "10.69": 1, "10.79": 1, "10.89": 1, "10.99": 1, "11.09": 1, "11.19": 1, "11.29": 1, "11.39": 1, "11.49": 1, "11.59": 1, "11.69": 1, "11.79": 1, "11.89": 1, "11.99": 1, "12.09": 1, "12.19": 1, "12.29": 1, "12.39": 1, "12.49": 1, "12.59": 1, "12.69": 1, "12.79": 1, "12.89": 1, "12.99": 1, "13.09": 1, "13.19": 1, "13.29": 1, "13.39": 1, "13.49": 1, "13.59": 1, "13.69": 1, "13.79": 1, "13.89": 1, "13.99": 1, "14.09": 1, "14.19": 1, "14.29": 1, "14.39": 1, "14.49": 1, "14.59": 1, "14.69": 1, "14.79": 1, "14.89": 1, "14.99": 1, "15.09": 1, "15.19": 1, "15.29": 1, "15.39": 1, "15.49": 1, "15.59": 1, "15.69": 1, "15.79": 1, "15.89": 1, "15.99": 1, "16.09": 1, "16.19": 1, "16.29": 1, "16.39": 1, "16.49": 1, "16.59": 1, "16.69": 1, "16.79": 1, "16.89": 1, "16.99": 1, "17.09": 1, "17.19": 1, "17.29": 1, "17.39": 1, "17.49": 1, "17.59": 1, "17.69": 1, "17.79": 1, "17.89": 1, "17.99": 1, "18.09": 1, "18.19": 1, "18.29": 1, "18.39": 1, "18.49": 1, "18.59": 1, "18.69": 1, "18.79": 1, "18.89": 1, "18.99": 1, "19.09": 1, "19.19": 1, "19.29": 1, "19.39": 1, "19.49": 1, "19.59": 1, "19.69": 1, "19.79": 1, "19.89": 1, "19.99": 1, "20.09": 1, "20.19": 1, "20.29": 1, "20.39": 1, "20.49": 1, "20.59": 1, "20.69": 1, "20.79": 1, "20.89": 1, "20.99": 1, "21.09": 1, "21.19": 1, "21.29": 1, "21.39": 1, "21.49": 1, "21.59": 1, "21.69": 1, "21.79": 1, "21.89": 1, "21.99": 1, "22.09": 1, "22.19": 1, "22.29": 1, "22.39": 1, "22.49": 1, "22.59": 1, "22.69": 1, "22.79": 1, "22.89": 1, "22.99": 1, "23.09": 1, "23.19": 1, "23.29": 1, "23.39": 1, "23.49": 1, "23.59": 1, "23.69": 1, "23.79": 1, "23.89": 1, "23.99": 1, "24.09": 1, "24.19": 1, "24.29": 1, "24.39": 1, "24.49": 1, "24.59": 1, "24.69": 1, "24.79": 1, "24.89": 1, "24.99": 1, "25.09": 1, "25.19": 1, "25.29": 1, "25.39": 1, "25.49": 1, "25.59": 1, "25.69": 1, "25.79": 1, "25.89": 1, "25.99": 1, "26.09": 1, "26.19": 1, "26.29": 1, "26.39": 1, "26.49": 1, "26.59": 1, "26.69": 1, "26.79": 1, "26.89": 1, "26.99": 1, "27.09": 1, "27.19": 1, "27.29": 1, "27.39": 1, "27.49": 1, "27.59": 1, "27.69": 1, "27.79": 1, "27.89": 1, "27.99": 1, "28.09": 1, "28.19": 1, "28.29": 1, "28.39": 1, "28.49": 1, "28.59": 1, "28.69": 1, "28.79": 1, "28.89": 1, "28.99": 1, "29.09": 1, "29.19": 1, "29.29": 1, "29.39": 1, "29.49": 1, "29.59": 1, "29.69": 1, "29.79": 1, "29.89": 1, "29.99": 1, "30.09": 1, "30.19": 1, "30.29": 1, "30.39": 1, "30.49": 1, "30.59": 1, "30.69": 1, "30.79": 1, "30.89": 1, "30.99": 1, "31.09": 1, "31.19": 1, "31.29": 1, "31.39": 1, "31.49": 1, "31.59": 1, "31.69": 1, "31.79": 1, "31.89": 1, "31.99": 1, "32.09": 1, "32.19": 1, "32.29": 1, "32.39": 1, "32.49": 1, "32.59": 1, "32.69": 1, "32.79": 1, "32.89": 1, "32.99": 1, "33.09": 1, "33.19": 1, "33.29": 1, "33.39": 1, "33.49": 1, "33.59": 1, "33.69": 1, "33.79": 1}, "max_k_depth": 0.61, "max_k_value": 2, "critical_depth_assessment": {"depth": 0.61, "k_value": 2, "profile_assessments": {}, "calculation_process": "公式: K(i) = int[(∑I(j,i)² / ∑I(j,i)) + 0.5]\n    I(j,i)值: [2, 1, 2]\n    ∑I(j,i)² = 2² + 1² + 2² = 4 + 1 + 4 = 9\n    ∑I(j,i) = 2 + 1 + 2 = 5\n    计算: (9 / 5) + 0.5 = 1.80 + 0.5 = 2.30\n    取整: int(2.30) = 2", "total_profiles": 0}, "iji_value_details": {}, "raw_parameter_data": {}, "continuous_k_range_analysis": [{"start_depth": 0.61, "end_depth": 1.39, "k_value": 2, "range_length_m": 0.7799999999999999, "range_length_cm": 77, "depth_points": [0.61, 0.69, 0.71, 0.79, 0.81, 0.89, 0.99, 1.09, 1.19, 1.29, 1.39], "point_count": 11}], "assessment_criteria": ["K值分布统计:", "K=1: 324个截面 (96.7%)", "K=2: 11个截面 (3.3%)", "总计分析截面: 335个", "最大K值位置: 深度0.61m处，K值=2", "相同最大K值出现位置: 0.61m, 0.69m, 0.71m, 0.79m, 0.81m, 0.89m, 0.99m, 1.09m, 1.19m, 1.29m, 1.39m", "* 连续范围1：深度 0.6m - 1.4m（连续长度：0.8m）", "* 涉及深度：0.6m, 0.7m, 0.7m, 0.8m, 0.8m, 0.9m, 1.0m, 1.1m, 1.2m, 1.3m, 1.4m"], "analysis_configuration": ["K值计算深度范围: 已启用 (0.5m / 50cm)", "启用指标: 声速, 波幅, 能量", "深度分析范围: 全部范围 (默认)", "判定依据: 新版GZ Traditional Analysis分类规则"]}, "source_file": "pile_integrity_analysis_20250816_092432.txt", "data_version": "v2.0_enhanced", "export_info": {"export_time": "2025-08-16T09:24:32.289571", "export_version": "v2.0_enhanced", "system_info": {"analysis_engine": "GZ Traditional Analysis Method", "data_processing_version": "2025 Standard", "json_format_version": "2.0"}}}