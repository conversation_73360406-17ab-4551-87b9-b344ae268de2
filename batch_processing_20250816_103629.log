2025-08-16 10:36:29,029 - INFO - === BATCH PROCESSING MODE INITIALIZED ===
2025-08-16 10:36:29,030 - INFO - Log file: batch_processing_20250816_103629.log
2025-08-16 10:36:29,030 - INFO - Directory exists: raw/
2025-08-16 10:36:29,030 - INFO - Directory exists: processed/
2025-08-16 10:36:29,030 - INFO - === STARTING BATCH ANALYSIS ===
2025-08-16 10:36:29,031 - INFO - Discovered 5 original TXT files for batch processing:
2025-08-16 10:36:29,031 - INFO -   1. 10075   34-3.TXT
2025-08-16 10:36:29,031 - INFO -   2. 10081   13-5.TXT
2025-08-16 10:36:29,031 - INFO -   3. 10107   6-2.TXT
2025-08-16 10:36:29,032 - INFO -   4. 10137   244-1.TXT
2025-08-16 10:36:29,032 - INFO -   5. 10173   67-6.TXT
2025-08-16 10:36:29,032 - INFO - Processing file 1/5: 10075   34-3.TXT (Pile ID: 10075   34-3)
2025-08-16 10:36:29,033 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10075   34-3 ===
2025-08-16 10:36:29,033 - INFO - [STAGE 1] Data extraction for 10075   34-3
2025-08-16 10:36:29,033 - INFO - Running: 数据导出 - 10075   34-3
2025-08-16 10:36:29,033 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 10:36:30,245 - INFO - [STAGE 1] SUCCESS: 10075   34-3_recreated.txt generated
2025-08-16 10:36:30,249 - INFO - [STAGE1] Moved 10075   34-3_recreated.txt to raw/
2025-08-16 10:36:30,249 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 10:36:30,249 - INFO - [STAGE 2] Data processing for 10075   34-3
2025-08-16 10:36:30,249 - INFO - Executing: 数据处理 - 10075   34-3 (reading from raw/)
2025-08-16 10:36:30,252 - INFO - Temporarily copied 10075   34-3_recreated.txt from raw/ to current directory
2025-08-16 10:36:30,252 - INFO - Running: 数据处理 - 10075   34-3
2025-08-16 10:36:30,252 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 10:36:32,474 - INFO - Cleaned up temporary file: 10075   34-3_recreated.txt
2025-08-16 10:36:32,474 - INFO - [STAGE 2] SUCCESS: 10075   34-3_processed.txt generated
2025-08-16 10:36:32,475 - INFO - [STAGE2] Moved 10075   34-3_processed.txt to processed/
2025-08-16 10:36:32,475 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 10:36:32,475 - INFO - [STAGE 3] Integrity analysis for 10075   34-3
2025-08-16 10:36:32,475 - INFO - Executing: 桩身完整性分析 - 10075   34-3 (reading from processed/)
2025-08-16 10:36:32,476 - INFO - Temporarily copied 10075   34-3_processed.txt from processed/ to current directory
2025-08-16 10:36:32,476 - INFO - Running: 桩身完整性分析 - 10075   34-3
2025-08-16 10:36:32,476 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 10:36:33,091 - INFO - Cleaned up temporary file: 10075   34-3_processed.txt
2025-08-16 10:36:33,091 - INFO - [STAGE 3] Completed for 10075   34-3
2025-08-16 10:36:33,105 - INFO - [RESULTS] Successfully extracted for 10075   34-3
2025-08-16 10:36:33,107 - INFO - [JSON] Successfully exported for 10075   34-3: pile_analysis_results_10075   34-3_20250816_103633.json
2025-08-16 10:36:33,107 - INFO - === PILE 10075   34-3 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 10:36:33,108 - INFO - SUCCESS: 10075   34-3 - Enhanced sequential processing completed
2025-08-16 10:36:33,109 - INFO - Processing file 2/5: 10081   13-5.TXT (Pile ID: 10081   13-5)
2025-08-16 10:36:33,109 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10081   13-5 ===
2025-08-16 10:36:33,109 - INFO - [STAGE 1] Data extraction for 10081   13-5
2025-08-16 10:36:33,109 - INFO - Running: 数据导出 - 10081   13-5
2025-08-16 10:36:33,109 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 10:36:39,176 - INFO - [STAGE 1] SUCCESS: 10081   13-5_recreated.txt generated
2025-08-16 10:36:39,193 - INFO - [STAGE1] Moved 10081   13-5_recreated.txt to raw/
2025-08-16 10:36:39,193 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 10:36:39,193 - INFO - [STAGE 2] Data processing for 10081   13-5
2025-08-16 10:36:39,194 - INFO - Executing: 数据处理 - 10081   13-5 (reading from raw/)
2025-08-16 10:36:39,203 - INFO - Temporarily copied 10081   13-5_recreated.txt from raw/ to current directory
2025-08-16 10:36:39,203 - INFO - Running: 数据处理 - 10081   13-5
2025-08-16 10:36:39,203 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 10:36:42,670 - INFO - Cleaned up temporary file: 10081   13-5_recreated.txt
2025-08-16 10:36:42,670 - INFO - [STAGE 2] SUCCESS: 10081   13-5_processed.txt generated
2025-08-16 10:36:42,671 - INFO - [STAGE2] Moved 10081   13-5_processed.txt to processed/
2025-08-16 10:36:42,671 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 10:36:42,671 - INFO - [STAGE 3] Integrity analysis for 10081   13-5
2025-08-16 10:36:42,672 - INFO - Executing: 桩身完整性分析 - 10081   13-5 (reading from processed/)
2025-08-16 10:36:42,672 - INFO - Temporarily copied 10081   13-5_processed.txt from processed/ to current directory
2025-08-16 10:36:42,672 - INFO - Running: 桩身完整性分析 - 10081   13-5
2025-08-16 10:36:42,672 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 10:36:43,287 - INFO - Cleaned up temporary file: 10081   13-5_processed.txt
2025-08-16 10:36:43,287 - INFO - [STAGE 3] Completed for 10081   13-5
2025-08-16 10:36:43,325 - INFO - [RESULTS] Successfully extracted for 10081   13-5
2025-08-16 10:36:43,327 - INFO - [JSON] Successfully exported for 10081   13-5: pile_analysis_results_10081   13-5_20250816_103643.json
2025-08-16 10:36:43,327 - INFO - === PILE 10081   13-5 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 10:36:43,327 - INFO - SUCCESS: 10081   13-5 - Enhanced sequential processing completed
2025-08-16 10:36:43,329 - INFO - Processing file 3/5: 10107   6-2.TXT (Pile ID: 10107   6-2)
2025-08-16 10:36:43,329 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10107   6-2 ===
2025-08-16 10:36:43,329 - INFO - [STAGE 1] Data extraction for 10107   6-2
2025-08-16 10:36:43,329 - INFO - Running: 数据导出 - 10107   6-2
2025-08-16 10:36:43,329 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 10:36:45,828 - INFO - [STAGE 1] SUCCESS: 10107   6-2_recreated.txt generated
2025-08-16 10:36:45,834 - INFO - [STAGE1] Moved 10107   6-2_recreated.txt to raw/
2025-08-16 10:36:45,834 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 10:36:45,834 - INFO - [STAGE 2] Data processing for 10107   6-2
2025-08-16 10:36:45,834 - INFO - Executing: 数据处理 - 10107   6-2 (reading from raw/)
2025-08-16 10:36:45,841 - INFO - Temporarily copied 10107   6-2_recreated.txt from raw/ to current directory
2025-08-16 10:36:45,842 - INFO - Running: 数据处理 - 10107   6-2
2025-08-16 10:36:45,843 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 10:36:48,713 - INFO - Cleaned up temporary file: 10107   6-2_recreated.txt
2025-08-16 10:36:48,713 - INFO - [STAGE 2] SUCCESS: 10107   6-2_processed.txt generated
2025-08-16 10:36:48,714 - INFO - [STAGE2] Moved 10107   6-2_processed.txt to processed/
2025-08-16 10:36:48,715 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 10:36:48,715 - INFO - [STAGE 3] Integrity analysis for 10107   6-2
2025-08-16 10:36:48,715 - INFO - Executing: 桩身完整性分析 - 10107   6-2 (reading from processed/)
2025-08-16 10:36:48,715 - INFO - Temporarily copied 10107   6-2_processed.txt from processed/ to current directory
2025-08-16 10:36:48,716 - INFO - Running: 桩身完整性分析 - 10107   6-2
2025-08-16 10:36:48,716 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 10:36:49,231 - INFO - Cleaned up temporary file: 10107   6-2_processed.txt
2025-08-16 10:36:49,231 - INFO - [STAGE 3] Completed for 10107   6-2
2025-08-16 10:36:49,241 - INFO - [RESULTS] Successfully extracted for 10107   6-2
2025-08-16 10:36:49,247 - INFO - [JSON] Successfully exported for 10107   6-2: pile_analysis_results_10107   6-2_20250816_103649.json
2025-08-16 10:36:49,247 - INFO - === PILE 10107   6-2 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 10:36:49,247 - INFO - SUCCESS: 10107   6-2 - Enhanced sequential processing completed
2025-08-16 10:36:49,250 - INFO - Processing file 4/5: 10137   244-1.TXT (Pile ID: 10137   244-1)
2025-08-16 10:36:49,250 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10137   244-1 ===
2025-08-16 10:36:49,250 - INFO - [STAGE 1] Data extraction for 10137   244-1
2025-08-16 10:36:49,251 - INFO - Running: 数据导出 - 10137   244-1
2025-08-16 10:36:49,251 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 10:36:50,262 - INFO - [STAGE 1] SUCCESS: 10137   244-1_recreated.txt generated
2025-08-16 10:36:50,265 - INFO - [STAGE1] Moved 10137   244-1_recreated.txt to raw/
2025-08-16 10:36:50,266 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 10:36:50,266 - INFO - [STAGE 2] Data processing for 10137   244-1
2025-08-16 10:36:50,266 - INFO - Executing: 数据处理 - 10137   244-1 (reading from raw/)
2025-08-16 10:36:50,267 - INFO - Temporarily copied 10137   244-1_recreated.txt from raw/ to current directory
2025-08-16 10:36:50,268 - INFO - Running: 数据处理 - 10137   244-1
2025-08-16 10:36:50,268 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 10:36:51,988 - INFO - Cleaned up temporary file: 10137   244-1_recreated.txt
2025-08-16 10:36:51,988 - INFO - [STAGE 2] SUCCESS: 10137   244-1_processed.txt generated
2025-08-16 10:36:51,990 - INFO - [STAGE2] Moved 10137   244-1_processed.txt to processed/
2025-08-16 10:36:51,990 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 10:36:51,990 - INFO - [STAGE 3] Integrity analysis for 10137   244-1
2025-08-16 10:36:51,990 - INFO - Executing: 桩身完整性分析 - 10137   244-1 (reading from processed/)
2025-08-16 10:36:51,991 - INFO - Temporarily copied 10137   244-1_processed.txt from processed/ to current directory
2025-08-16 10:36:51,991 - INFO - Running: 桩身完整性分析 - 10137   244-1
2025-08-16 10:36:51,991 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 10:36:52,561 - INFO - Cleaned up temporary file: 10137   244-1_processed.txt
2025-08-16 10:36:52,561 - INFO - [STAGE 3] Completed for 10137   244-1
2025-08-16 10:36:52,589 - INFO - [RESULTS] Successfully extracted for 10137   244-1
2025-08-16 10:36:52,591 - INFO - [JSON] Successfully exported for 10137   244-1: pile_analysis_results_10137   244-1_20250816_103652.json
2025-08-16 10:36:52,591 - INFO - === PILE 10137   244-1 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 10:36:52,591 - INFO - SUCCESS: 10137   244-1 - Enhanced sequential processing completed
2025-08-16 10:36:52,594 - INFO - Processing file 5/5: 10173   67-6.TXT (Pile ID: 10173   67-6)
2025-08-16 10:36:52,595 - INFO - === ENHANCED SEQUENTIAL PROCESSING: 10173   67-6 ===
2025-08-16 10:36:52,595 - INFO - [STAGE 1] Data extraction for 10173   67-6
2025-08-16 10:36:52,595 - INFO - Running: 数据导出 - 10173   67-6
2025-08-16 10:36:52,595 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\connection_data.py
2025-08-16 10:36:54,015 - INFO - [STAGE 1] SUCCESS: 10173   67-6_recreated.txt generated
2025-08-16 10:36:54,019 - INFO - [STAGE1] Moved 10173   67-6_recreated.txt to raw/
2025-08-16 10:36:54,019 - INFO - [STAGE 1] File organization: 1 files moved to raw/
2025-08-16 10:36:54,020 - INFO - [STAGE 2] Data processing for 10173   67-6
2025-08-16 10:36:54,020 - INFO - Executing: 数据处理 - 10173   67-6 (reading from raw/)
2025-08-16 10:36:54,023 - INFO - Temporarily copied 10173   67-6_recreated.txt from raw/ to current directory
2025-08-16 10:36:54,023 - INFO - Running: 数据处理 - 10173   67-6
2025-08-16 10:36:54,024 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\process_data.py
2025-08-16 10:36:56,145 - INFO - Cleaned up temporary file: 10173   67-6_recreated.txt
2025-08-16 10:36:56,145 - INFO - [STAGE 2] SUCCESS: 10173   67-6_processed.txt generated
2025-08-16 10:36:56,146 - INFO - [STAGE2] Moved 10173   67-6_processed.txt to processed/
2025-08-16 10:36:56,146 - INFO - [STAGE 2] File organization: 1 files moved to processed/
2025-08-16 10:36:56,146 - INFO - [STAGE 3] Integrity analysis for 10173   67-6
2025-08-16 10:36:56,146 - INFO - Executing: 桩身完整性分析 - 10173   67-6 (reading from processed/)
2025-08-16 10:36:56,146 - INFO - Temporarily copied 10173   67-6_processed.txt from processed/ to current directory
2025-08-16 10:36:56,147 - INFO - Running: 桩身完整性分析 - 10173   67-6
2025-08-16 10:36:56,147 - INFO - Script path: E:\桩基完整性系统\final_test\07-05\testing III - 副本 - 副本 - 副本\pile_analysis_processor.py
2025-08-16 10:36:56,619 - INFO - Cleaned up temporary file: 10173   67-6_processed.txt
2025-08-16 10:36:56,619 - INFO - [STAGE 3] Completed for 10173   67-6
2025-08-16 10:36:56,654 - INFO - [RESULTS] Successfully extracted for 10173   67-6
2025-08-16 10:36:56,656 - INFO - [JSON] Successfully exported for 10173   67-6: pile_analysis_results_10173   67-6_20250816_103656.json
2025-08-16 10:36:56,657 - INFO - === PILE 10173   67-6 ENHANCED SEQUENTIAL PROCESSING COMPLETED (3/3 stages) ===
2025-08-16 10:36:56,657 - INFO - SUCCESS: 10173   67-6 - Enhanced sequential processing completed
2025-08-16 10:36:56,660 - INFO - === BATCH PROCESSING SUMMARY REPORT ===
2025-08-16 10:36:56,661 - INFO - Total files processed: 5
2025-08-16 10:36:56,661 - INFO - Successful: 5
2025-08-16 10:36:56,662 - INFO - Failed: 0
2025-08-16 10:36:56,662 - INFO - Success rate: 100.0%
2025-08-16 10:36:56,662 - INFO - 
=== DETAILED RESULTS ===
