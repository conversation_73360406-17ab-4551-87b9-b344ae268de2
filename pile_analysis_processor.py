#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桩基分析处理器 - 从pile_analysis.py导入数据进行GZ传统分析 (已修正)
Pile Analysis Processor - Import data from pile_analysis.py for GZ Traditional Analysis (Corrected)
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入相关模块
try:
    from pile_analysis import get_processed_data
    PILE_ANALYSIS_AVAILABLE = True
except ImportError:
    print("[警告] pile_analysis模块不可用，将仅使用txt文件模式")
    PILE_ANALYSIS_AVAILABLE = False
    def get_processed_data(pile_id):
        return None

try:
    from gz_analysis_gui import GZTraditionalAnalyzer
    GZ_ANALYZER_AVAILABLE = True
except ImportError:
    print("[警告] gz_analysis_gui模块不可用，将使用简化分析器")
    GZ_ANALYZER_AVAILABLE = False

    # 创建简化的分析器类
    class SimpleAnalyzer:
        def __init__(self):
            self.data = None
            self.using_normalized_energy = False

        def load_data_from_dataframe(self, df):
            self.data = df
            return True

        def run_analysis(self):
            if self.data is None:
                return None

            # 简化的分析逻辑
            return self._simple_integrity_analysis()

        def _simple_integrity_analysis(self):
            """GZ传统分析法 - 基于声速、波幅、能量三项指标"""
            results = {
                'final_category': 'I类桩',
                'enabled_indicators': {'speed': True, 'amplitude': True, 'energy': True, 'psd': False},
                'gz_depth_range': 0.5,
                'report_details': [],
                'K_values': {},
                'I_ji_values': {},
                'raw_data': {},
                'K_calculation_details': {}
            }

            # GZ传统分析算法
            for _, row in self.data.iterrows():
                depth = row['Depth']

                # 计算各剖面的I(j,i)值
                I_ji_values = {}
                raw_data = {}

                # 动态识别所有剖面
                profiles = []
                profile_count = 1
                while True:
                    s_key = f'S{profile_count}'
                    a_key = f'A{profile_count}'
                    e_key = f'E{profile_count}'
                    if s_key not in row or a_key not in row or e_key not in row:
                        break
                    profiles.append(profile_count)
                    profile_count += 1

                # 为每个剖面计算I(j,i)值
                # 获取实际的剖面名称（从数据中读取）
                actual_profile_names = row.get('profile_names', [])

                for idx, profile_num in enumerate(profiles, 1):
                    # 使用实际的剖面名称，而不是硬编码的名称
                    if idx-1 < len(actual_profile_names):
                        profile_name = actual_profile_names[idx-1]
                    else:
                        profile_name = f'profile_{idx}'

                    speed = row.get(f'S{profile_num}', 100)
                    amp = row.get(f'A{profile_num}', 0)
                    energy = row.get(f'E{profile_num}', 1.0)



                    I_ji = self._calculate_I_ji(speed, amp, energy, depth)
                    I_ji_values[profile_name] = I_ji
                    raw_data[profile_name] = {'speed': speed, 'amplitude': amp, 'energy': energy}

                # 计算K值
                I_ji_list = list(I_ji_values.values())
                k_value, calc_details = self._calculate_K_value(I_ji_list)

                # 存储结果
                results['K_values'][depth] = k_value
                results['I_ji_values'][depth] = I_ji_values
                results['raw_data'][depth] = raw_data
                results['K_calculation_details'][depth] = {
                    'method': 'current_depth',
                    'calculation_detail': calc_details
                }

            # 根据K值分布判定桩基完整性类别
            k_values = list(results['K_values'].values())
            final_category, problem_depths = self._determine_pile_category(k_values, results['K_values'])
            results['final_category'] = final_category

            # 生成判定依据 (新版标准)
            k_values_map = results['K_values']
            k2_count = list(k_values_map.values()).count(2)
            k3_count = list(k_values_map.values()).count(3)
            k4_count = list(k_values_map.values()).count(4)

            if final_category == 'IV类桩':
                if k4_count == 1:
                    k4_depths = [f"{d:.2f}m" for d, k in k_values_map.items() if k == 4]
                    results['report_details'].append(f'仅存在一个K=4，位于深度: {", ".join(k4_depths)}。')
                elif k4_count > 1:
                    k4_depths = [f"{d:.2f}m" for d, k in k_values_map.items() if k == 4]
                    results['report_details'].append(f'存在多个K=4，位于深度: {", ".join(k4_depths)}。')
                elif self._has_continuous_k_in_50cm_range(k_values_map, 3):
                    # 生成详细的连续范围信息
                    continuous_info = self._generate_continuous_range_details(k_values_map, 3)
                    results['report_details'].append(continuous_info)
            elif final_category == 'III类桩':
                if k3_count == 1:
                    k3_depths = [f"{d:.2f}m" for d, k in k_values_map.items() if k == 3]
                    results['report_details'].append(f'仅存在一个K=3，位于深度: {", ".join(k3_depths)}。')
                elif k3_count > 1 and self._all_k3_spacing_ge_50cm(k_values_map):
                    k3_depths = [f"{d:.2f}m" for d, k in k_values_map.items() if k == 3]
                    results['report_details'].append(f'多个K=3且距离≥50cm，位于深度: {", ".join(k3_depths)}。')
                elif self._has_continuous_k_in_50cm_range(k_values_map, 2):
                    # 生成详细的连续范围信息
                    continuous_info = self._generate_continuous_range_details(k_values_map, 2)
                    results['report_details'].append(continuous_info)
            elif final_category == 'II类桩':
                if k2_count == 1:
                    k2_depths = [f"{d:.2f}m" for d, k in k_values_map.items() if k == 2]
                    results['report_details'].append(f'仅存在一个K=2，位于深度: {", ".join(k2_depths)}。')
                elif k2_count > 1 and not self._has_continuous_k_in_50cm_range(k_values_map, 2):
                    k2_depths = [f"{d:.2f}m" for d, k in k_values_map.items() if k == 2]
                    results['report_details'].append(f'多个K=2但不存在50cm连续范围，位于深度: {", ".join(k2_depths)}。')
            else:
                results['report_details'].append('所有检测截面Ki值均为1。')

            return results

        def _calculate_I_ji(self, speed, amplitude, energy, depth=None):
            """
            计算单个剖面的I(j,i)值 - 增强版（含I=4降级规则）
            使用2025年最新的综合判定标准，基于相对声速、波幅差和归一化能量

            增强功能：
            - 针对I=4情况实现"2 out of 3 pass"降级规则
            - 当基础分类为I=4但有2个参数达标时，自动降级为I=3

            参数:
            - speed: 相对声速百分比 V%
            - amplitude: 波幅差 Amp (dB)
            - energy: 归一化能量百分比 E%
            - depth: 深度 (可选，用于获取深度相关的临界值)

            返回: I(j,i)值 (1-4) 或 "N/A"（数据无效时）
            """
            import math

            # 检查数据有效性 - N/A值处理
            invalid_params = []

            # 检查关键参数是否为None或NaN
            nan_params = []

            if speed is None:
                invalid_params.append("声速为None")
            elif isinstance(speed, (int, float)) and math.isnan(speed):
                nan_params.append("声速为NaN")

            if amplitude is None:
                invalid_params.append("波幅为None")
            elif isinstance(amplitude, (int, float)) and math.isnan(amplitude):
                nan_params.append("波幅为NaN")

            if energy is None:
                invalid_params.append("能量为None")
            elif isinstance(energy, (int, float)) and math.isnan(energy):
                nan_params.append("能量为NaN")

            # 如果所有关键参数都是NaN，返回N/A
            if len(nan_params) >= 3:  # 所有三个参数都是NaN
                if depth is not None:
                    print(f"[N/A] 深度{depth:.2f}m: 所有参数为NaN，I(j,i) = N/A ({', '.join(nan_params)})")
                else:
                    print(f"[N/A] 所有参数为NaN，I(j,i) = N/A ({', '.join(nan_params)})")
                return "N/A"

            # 如果大部分参数是NaN（2个或以上），也返回N/A
            elif len(nan_params) >= 2:
                if depth is not None:
                    print(f"[N/A] 深度{depth:.2f}m: 多数参数为NaN，I(j,i) = N/A ({', '.join(nan_params)})")
                else:
                    print(f"[N/A] 多数参数为NaN，I(j,i) = N/A ({', '.join(nan_params)})")
                return "N/A"

            # 如果关键参数缺失，返回N/A
            if invalid_params:
                if depth is not None:
                    print(f"[N/A] 深度{depth:.2f}m: 数据无效，I(j,i) = N/A ({', '.join(invalid_params)})")
                else:
                    print(f"[N/A] 数据无效，I(j,i) = N/A ({', '.join(invalid_params)})")
                return "N/A"

            # 将参数转换为标准格式
            try:
                V_percent = float(speed)  # 相对声速(V%)
                Amp_dB = float(amplitude)  # 波幅差(Amp) dB
                E_percent = float(energy)  # 归一化能量(E%)
            except (ValueError, TypeError):
                if depth is not None:
                    print(f"[N/A] 深度{depth:.2f}m: 参数类型转换失败，I(j,i) = N/A")
                else:
                    print(f"[N/A] 参数类型转换失败，I(j,i) = N/A")
                return "N/A"

            # 按照2025年最新分层判定标准计算I(j,i)值
            # 分层判定逻辑：按优先级顺序检查，确保科学准确性

            # I=1 (完整): 最高优先级 - 必须满足任一完整条件
            if self._check_I_equals_1_new(V_percent, Amp_dB, E_percent):
                return 1

            # I=2 (轻微缺陷): 第二优先级 - 不满足I=1且满足任一轻微缺陷条件
            elif self._check_I_equals_2_new(V_percent, Amp_dB, E_percent):
                return 2

            # I=3 (显著缺陷): 第三优先级 - 不满足I=1,I=2且满足任一显著缺陷条件
            elif self._check_I_equals_3_new(V_percent, Amp_dB, E_percent):
                return 3

            # I=4 (严重缺陷): 最低优先级 - 仅对真正的I=4案例应用增强分类逻辑
            else:
                # 首先检查是否真正满足I=4条件
                if self._check_I_equals_4_new(V_percent, Amp_dB, E_percent):
                    # 只有真正满足I=4条件的案例才应用增强降级规则
                    enhanced_I_ji = self._apply_enhanced_I4_classification(V_percent, Amp_dB, E_percent, depth)
                    return enhanced_I_ji
                else:
                    # 不满足任何I=1,I=2,I=3,I=4条件的边界案例，使用保守的默认分类
                    # 这些通常是边界条件问题，默认为I=4以保持保守性
                    if depth is not None:
                        print(f"[边界默认] 深度{depth:.2f}m: 不满足任何标准条件，默认分类I=4")
                    else:
                        print(f"[边界默认] 不满足任何标准条件，默认分类I=4")
                    return 4

        def _format_parameter_values(self, V_percent, Amp_dB, E_percent):
            """格式化参数值显示，处理NaN值"""
            import math

            # 格式化声速
            if isinstance(V_percent, (int, float)) and math.isnan(V_percent):
                speed_str = "声速: 数据缺失"
            else:
                speed_str = f"声速{V_percent:.1f}%"

            # 格式化波幅
            if isinstance(Amp_dB, (int, float)) and math.isnan(Amp_dB):
                amp_str = "波幅: 数据缺失"
            else:
                amp_str = f"波幅{Amp_dB:.1f}dB"

            # 格式化能量
            if isinstance(E_percent, (int, float)) and math.isnan(E_percent):
                energy_str = "能量: 数据缺失"
            else:
                energy_str = f"能量{E_percent:.2f}"

            return f"{speed_str}, {amp_str}, {energy_str}"

        def _check_I_equals_1_new(self, V_percent, Amp_dB, E_percent):
            """
            检查是否满足I=1的条件 (2025年最新标准)
            I=1 (完整): 声速和波幅参数均无异常，实测波形正常；或声速或波幅存在轻微异常，实测波形畸变不明显
            """
            # 条件1: 相对声速(V%) > 100%，且波幅差(Amp) ≤ 0dB，且归一化能量(E%) > 0.8
            condition1 = (V_percent > 100 and Amp_dB <= 0 and E_percent > 0.8)

            # 条件2: 85% < 相对声速(V%) ≤ 100%，且波幅差(Amp) ≤ 0dB，且归一化能量(E%) > 0.8
            condition2 = (V_percent > 85 and V_percent <= 100 and Amp_dB <= 0 and E_percent > 0.8)

            # 条件3: 相对声速(V%) > 100%，且波幅差(Amp) ≤ 4dB，且归一化能量(E%) > 0.8
            condition3 = (V_percent > 100 and Amp_dB <= 4 and E_percent > 0.8)
            
            # [二次修复] 针对您提出的问题进行优化：当声速和波幅表现优异时，适当放宽对能量的要求
            # 新增条件4: 声速合格(>85%)且波幅无衰减(<=0)，允许能量略低(>=0.7)的情况被评为I类
            condition4 = (V_percent > 85 and V_percent <= 100 and Amp_dB <= 0 and E_percent >= 0.7)

            return condition1 or condition2 or condition3 or condition4

        def _check_I_equals_2_new(self, V_percent, Amp_dB, E_percent):
            """
            检查是否满足I=2的条件 (2025年最新标准 - 修复版)
            I=2 (轻微缺陷): 声速和波幅参数均存在轻微异常，实测波形畸变较明显；或声速或波幅存在较明显异常，实测波形畸变较明显
            """
            # 条件1: 85% < 相对声速(V%)，且波幅差(Amp) ≤ 4dB，且 0.5 < 归一化能量(E%) ≤ 0.8
            condition1 = (V_percent > 85 and Amp_dB <= 4 and 0.5 < E_percent <= 0.8)

            # 条件2: 75% < 相对声速(V%) ≤ 85%，且波幅差(Amp) ≤ 4dB，且归一化能量(E%) > 0.5
            # 注意：这里需要包含边界值，所以使用 >= 75 和 <= 85
            condition2 = (V_percent > 75 and V_percent <= 85 and Amp_dB <= 4 and E_percent >= 0.5)

            # 条件3: 相对声速(V%) > 85%，且 4dB < 波幅差(Amp) ≤ 8dB，且归一化能量(E%) > 0.5
            condition3 = (V_percent > 85 and Amp_dB > 4 and Amp_dB <= 8 and E_percent > 0.5)

            # 条件4: 相对声速(V%) > 85%，且波幅差(Amp) ≤ 4dB，且 0.20 < 归一化能量(E%) ≤ 0.5
            # 修复逻辑缺陷：好的声速和波幅但中等能量应归类为I=2而非I=4
            condition4 = (V_percent > 85 and Amp_dB <= 4 and 0.20 < E_percent <= 0.5)

            # 条件5: 85% < 相对声速(V%) < 100%，且 0 < 波幅差(Amp) ≤ 4dB，且归一化能量(E%) > 0.8
            # 修复关键逻辑缺陷：声速略低于100%但波幅和能量良好的情况应归类为I=2轻微缺陷
            condition5 = (V_percent > 85 and V_percent < 100 and Amp_dB > 0 and Amp_dB <= 4 and E_percent > 0.8)

            return condition1 or condition2 or condition3 or condition4 or condition5

        def _check_I_equals_3_new(self, V_percent, Amp_dB, E_percent):
            """
            检查是否满足I=3的条件 (2025年最新标准 - 已修正)
            I=3 (显著缺陷): 声速和波幅参数均存在较明显异常，实测波形畸变明显；或声速或波幅存在明显异常，实测波形畸变明显
            """
            # 条件1: 75% < 相对声速(V%) ≤ 85%，且 4dB < 波幅差(Amp) ≤ 8dB，且 0.25 < 归一化能量(E%) ≤ 0.5
            condition1 = (V_percent > 75 and V_percent <= 85 and Amp_dB > 4 and Amp_dB <= 8 and 0.25 < E_percent <= 0.5)

            # [已修正] 条件2: 修正逻辑以匹配报告中的规则 (原: Amp_dB <= 12, E_percent >= 0.25)
            condition2 = (V_percent > 65 and V_percent <= 75 and Amp_dB >= 8 and Amp_dB <= 12 and E_percent > 0.25)

            # [已修正] 条件3: 修正逻辑以匹配报告中的规则 (原: V_percent > 65)
            condition3 = (V_percent > 75 and Amp_dB > 8 and Amp_dB <= 12 and E_percent > 0.25)

            return condition1 or condition2 or condition3

        def _check_I_equals_4_new(self, V_percent, Amp_dB, E_percent):
            """
            检查是否满足I=4的条件 (2025年最新标准 - 分层判定版)
            I=4 (严重缺陷): 不满足I=1, I=2, I=3条件，或满足以下任一严重缺陷条件

            新的分层判定标准：
            - 严重条件1: V% ≤ 65%
            - 严重条件2: Amp > 12dB
            - 严重条件3: E% < 0.25
            """
            # 严重条件1: 相对声速(V%) ≤ 65%
            severe_condition1 = (V_percent <= 65)

            # 严重条件2: 波幅差(Amp) > 12dB
            severe_condition2 = (Amp_dB > 12)

            # 严重条件3: 归一化能量(E%) < 0.25
            severe_condition3 = (E_percent < 0.25)

            return severe_condition1 or severe_condition2 or severe_condition3

        def _apply_enhanced_I4_classification(self, V_percent, Amp_dB, E_percent, depth=None):
            """
            应用增强的I=4分类逻辑 - 基于条件状态显示的"2 out of 3 pass"降级规则

            核心逻辑修复：
            - 基于条件状态显示的✓和✗数量来决定是否降级
            - 如果2个条件显示✓，1个显示✗ → 降级为I=3
            - 其他情况 → 保持I=4

            条件状态显示逻辑（针对I=4）：
            - 声速: >65% 显示✓, ≤65% 显示✗
            - 波幅: >12dB 显示✓, ≤12dB 显示✗
            - 能量: <0.25 显示✓, ≥0.25 显示✗

            参数:
            - V_percent: 相对声速百分比
            - Amp_dB: 波幅差
            - E_percent: 归一化能量百分比
            - depth: 深度（可选，用于日志记录）

            返回: 最终的I(j,i)值 (3或4)
            """
            try:
                # 基于I=4的条件状态显示逻辑检查各个条件
                speed_condition_met = V_percent > 65  # 声速>65%显示✓
                amplitude_condition_met = Amp_dB > 12  # 波幅>12dB显示✓
                energy_condition_met = E_percent < 0.25  # 能量<0.25显示✓

                # 统计满足条件状态显示的数量（显示✓的数量）
                condition_met_count = sum([speed_condition_met, amplitude_condition_met, energy_condition_met])

                # 应用基于条件状态显示的"2 out of 3 pass"降级规则
                if condition_met_count == 2:
                    # 恰好2个条件显示✓，1个显示✗ → 降级为I=3
                    not_met_params = []
                    if not speed_condition_met:
                        not_met_params.append(f"声速{V_percent:.1f}%≤65%")
                    if not amplitude_condition_met:
                        not_met_params.append(f"波幅{Amp_dB:.1f}dB≤12dB")
                    if not energy_condition_met:
                        not_met_params.append(f"能量{E_percent:.2f}≥0.25")

                    if depth is not None:
                        print(f"[I=4降级] 深度{depth:.2f}m: 2/3条件显示满足，降级 I=4→3 (不满足:{','.join(not_met_params)})")
                    else:
                        print(f"[I=4降级] 2/3条件显示满足，降级 I=4→3 (不满足:{','.join(not_met_params)})")

                    return 3

                else:
                    # 其他情况（3个✓、1个✓或0个✓）→ 保持I=4
                    if depth is not None:
                        print(f"[I=4保持] 深度{depth:.2f}m: {condition_met_count}/3条件显示满足，保持分类I=4")
                    else:
                        print(f"[I=4保持] {condition_met_count}/3条件显示满足，保持分类I=4")
                    return 4

            except Exception as e:
                print(f"[警告] 增强I=4分类处理失败: {e}")
                return 4

        def _check_speed_severe_defect(self, V_percent):
            """
            检查声速是否满足严重缺陷条件
            严重缺陷条件：V% ≤ 65%
            """
            import math
            if isinstance(V_percent, (int, float)) and math.isnan(V_percent):
                # NaN声速视为严重缺陷（数据缺失本身就是严重问题）
                return True
            return V_percent <= 65

        def _check_amplitude_severe_defect(self, Amp_dB):
            """
            检查波幅差是否满足严重缺陷条件
            严重缺陷条件：Amp > 12dB
            """
            import math
            if isinstance(Amp_dB, (int, float)) and math.isnan(Amp_dB):
                # NaN波幅视为严重缺陷（数据缺失本身就是严重问题）
                return True
            return Amp_dB > 12

        def _check_energy_severe_defect(self, E_percent):
            """
            检查能量是否满足严重缺陷条件
            严重缺陷条件：E% < 0.25
            """
            import math
            if isinstance(E_percent, (int, float)) and math.isnan(E_percent):
                # NaN能量视为严重缺陷（数据缺失本身就是严重问题）
                return True
            return E_percent < 0.25

        def _calculate_K_value(self, I_ji_list):
            """计算K值 - 支持N/A值过滤"""
            if not I_ji_list or all(i == 0 for i in I_ji_list):
                return 1, {'note': '无有效数据，默认K=1', 'valid_I_ji': [], 'final_K': 1}

            # 过滤有效的I(j,i)值 - 排除N/A和无效值
            valid_I_ji = []
            na_count = 0

            for i in I_ji_list:
                if i == "N/A":
                    na_count += 1
                elif isinstance(i, (int, float)) and i > 0:
                    valid_I_ji.append(i)

            if not valid_I_ji:
                note = f'无有效I(j,i)值，默认K=1'
                if na_count > 0:
                    note += f' (包含{na_count}个N/A值)'
                return 1, {'note': note, 'valid_I_ji': [], 'na_count': na_count, 'final_K': 1}

            # 计算K值：K(i) = int[(∑I(j,i)² / ∑I(j,i)) + 0.5]
            sum_I_ji_sq = sum(i**2 for i in valid_I_ji)
            sum_I_ji = sum(valid_I_ji)
            ratio = sum_I_ji_sq / sum_I_ji
            K_i_float = ratio + 0.5
            final_K = int(K_i_float)

            calc_details = {
                'valid_I_ji': valid_I_ji,
                'sum_I_ji_sq': sum_I_ji_sq,
                'sum_I_ji': sum_I_ji,
                'ratio': ratio,
                'K_i_float': K_i_float,
                'final_K': final_K,
                'na_count': na_count,
                'note': f'计算完成 (有效值:{len(valid_I_ji)}个, N/A值:{na_count}个)' if na_count > 0 else '计算完成'
            }

            return final_K, calc_details

        def _determine_pile_category(self, k_values, k_values_map):
            """
            根据K值分布判定桩基完整性类别 (2025年最新标准)

            I类桩: 所有检测截面Ki值均为1
            II类桩: 仅存在一个K=2，或多个K=2但不存在50cm连续范围
            III类桩: 仅存在一个K=3，或多个K=3且距离≥50cm，或K=2存在50cm连续范围
            IV类桩: 仅存在一个K=4，或K=3存在50cm连续范围
            """
            if not k_values:
                return 'I类桩', 0

            # 统计各K值的数量
            k1_count = k_values.count(1)
            k2_count = k_values.count(2)
            k3_count = k_values.count(3)
            k4_count = k_values.count(4)

            # IV类桩判定 (2025年最新标准)
            if k4_count == 1:
                # 仅存在一个K=4
                return 'IV类桩', k4_count
            elif k4_count > 1:
                # 存在多个K=4，直接判定为IV类桩
                return 'IV类桩', k4_count
            elif self._has_continuous_k_in_50cm_range(k_values_map, 3):
                # K=3存在50cm连续范围
                return 'IV类桩', k3_count

            # III类桩判定 (2025年最新标准)
            elif k3_count == 1:
                # 仅存在一个K=3
                return 'III类桩', k3_count
            elif k3_count > 1:
                # 多个K=3的情况
                if self._all_k3_spacing_ge_50cm(k_values_map):
                    # 多个K=3且距离≥50cm
                    return 'III类桩', k3_count
                else:
                    # 多个K=3但距离<50cm，仍然是III类桩（因为存在K=3）
                    return 'III类桩', k3_count
            elif self._has_continuous_k_in_50cm_range(k_values_map, 2):
                # K=2存在50cm连续范围
                return 'III类桩', k2_count

            # II类桩判定 (2025年最新标准)
            elif k2_count == 1:
                # 仅存在一个K=2
                return 'II类桩', k2_count
            elif k2_count > 1 and not self._has_continuous_k_in_50cm_range(k_values_map, 2):
                # 多个K=2但不存在50cm连续范围
                return 'II类桩', k2_count

            # I类桩判定 (2025年最新标准)
            else:
                # 所有检测截面Ki值均为1
                return 'I类桩', 0

        def _has_continuous_k_in_50cm_range(self, k_values_map, target_k):
            """
            [修正后] 检查是否存在一个长度达到或超过50cm的连续缺陷区段。
            该区段内所有数据点的K值都必须为目标K值。
            """
            if not k_values_map:
                return False

            # 获取所有深度并按升序排序
            all_depths = sorted(k_values_map.keys())
            
            i = 0
            while i < len(all_depths):
                # 检查当前点是否为目标K值的起点
                if k_values_map[all_depths[i]] == target_k:
                    # 找到一个潜在连续区段的起点
                    start_depth = all_depths[i]
                    j = i
                    
                    # 向前查找这个连续区段的终点
                    # 只要下一个点的K值仍然是目标值，就继续延伸
                    while j + 1 < len(all_depths) and k_values_map[all_depths[j+1]] == target_k:
                        j += 1
                    
                    end_depth = all_depths[j]
                    
                    # 计算这个连续区段的实际地质长度
                    if end_depth - start_depth >= 0.5:
                        # 如果长度达到50cm，则判定为连续缺陷
                        return True
                    
                    # 从当前区段的下一个点继续搜索
                    i = j + 1
                else:
                    # 如果当前点不是目标K值，则继续检查下一个点
                    i += 1
                    
            return False

        def _generate_continuous_range_details(self, k_values_map, target_k):
            """
            生成详细的连续范围信息

            参数:
            - k_values_map: 深度-K值映射
            - target_k: 目标K值

            返回: 详细的连续范围描述字符串
            """
            try:
                # 分析连续范围
                continuous_ranges = self._analyze_continuous_k_ranges_for_processor(k_values_map, target_k)

                if not continuous_ranges:
                    return f'K={target_k}存在50cm连续范围。'

                # 筛选出长度≥50cm的连续范围
                valid_ranges = [r for r in continuous_ranges if r['range_length_m'] >= 0.5]

                if not valid_ranges:
                    return f'K={target_k}存在50cm连续范围。'

                # 构建详细信息
                details = [f'K={target_k}存在50cm连续范围：']

                # 按起始深度排序
                valid_ranges.sort(key=lambda x: x['start_depth'])

                # 添加每个连续范围的详细信息
                for i, range_info in enumerate(valid_ranges, 1):
                    start_depth = range_info['start_depth']
                    end_depth = range_info['end_depth']
                    range_length = range_info['range_length_m']

                    details.append(f'  * 连续范围{i}：深度 {start_depth:.1f}m - {end_depth:.1f}m（连续长度：{range_length:.1f}m）')

                # 添加涉及深度列表
                all_depths = []
                for range_info in valid_ranges:
                    all_depths.extend(range_info.get('depth_points', []))

                # 去重并排序
                unique_depths = sorted(list(set(all_depths)))
                if unique_depths:
                    depth_str = ", ".join([f"{d:.1f}m" for d in unique_depths])
                    details.append(f'  * 涉及深度：{depth_str}')

                return '\n'.join(details)

            except Exception as e:
                # 如果增强功能失败，回退到原始格式
                return f'K={target_k}存在50cm连续范围。'

        def _analyze_continuous_k_ranges_for_processor(self, k_values_map, target_k):
            """
            分析指定K值的连续深度范围（专用于processor）

            参数:
            - k_values_map: 深度-K值映射
            - target_k: 目标K值

            返回: 连续范围信息列表
            """
            continuous_ranges = []

            try:
                # 获取所有深度并排序
                all_depths = sorted(k_values_map.keys())

                if len(all_depths) < 3:  # 至少需要3个点才能形成范围
                    return continuous_ranges

                # 使用灵活的算法：寻找所有连续的目标K值序列
                current_sequence = []

                for depth in all_depths:
                    if k_values_map[depth] == target_k:
                        # 如果当前深度的K值匹配
                        if not current_sequence:
                            # 开始新序列
                            current_sequence = [depth]
                        else:
                            # 检查是否与前一个深度连续（间距≤0.25m视为连续，考虑浮点数精度）
                            gap = round(depth - current_sequence[-1], 2)
                            if gap <= 0.25:
                                current_sequence.append(depth)
                            else:
                                # 序列中断，检查当前序列是否满足50cm要求
                                self._check_and_add_sequence_for_processor(current_sequence, target_k, continuous_ranges)
                                # 开始新序列
                                current_sequence = [depth]
                    else:
                        # K值不匹配，检查当前序列
                        self._check_and_add_sequence_for_processor(current_sequence, target_k, continuous_ranges)
                        # 重置序列
                        current_sequence = []

                # 处理最后一个序列
                self._check_and_add_sequence_for_processor(current_sequence, target_k, continuous_ranges)

                return continuous_ranges

            except Exception as e:
                return continuous_ranges

        def _check_and_add_sequence_for_processor(self, sequence, target_k, continuous_ranges):
            """
            检查序列是否满足连续范围要求，如果满足则添加到结果中（专用于processor）

            参数:
            - sequence: 深度序列
            - target_k: 目标K值
            - continuous_ranges: 结果列表
            """
            if len(sequence) >= 3:  # 至少3个点
                sequence_range = sequence[-1] - sequence[0]
                if sequence_range >= 0.5:  # 50cm = 0.5m
                    range_info = {
                        'start_depth': sequence[0],
                        'end_depth': sequence[-1],
                        'k_value': target_k,
                        'range_length_m': sequence_range,
                        'range_length_cm': int(sequence_range * 100),
                        'depth_points': sequence.copy(),
                        'point_count': len(sequence)
                    }
                    continuous_ranges.append(range_info)

        def _all_k3_spacing_ge_50cm(self, k_values_map):
            """
            检查所有K=3截面之间的距离是否都≥50cm
            """
            if not k_values_map:
                return True

            # 获取所有K=3的深度并排序
            k3_depths = sorted([depth for depth, k in k_values_map.items() if k == 3])

            if len(k3_depths) < 2:
                return True  # 少于2个K=3，不需要检查距离

            # 检查每个相邻K=3之间的距离是否都≥50cm
            for i in range(len(k3_depths) - 1):
                distance = k3_depths[i+1] - k3_depths[i]
                if distance < 0.5:  # 50cm = 0.5m
                    return False

            return True



    GZTraditionalAnalyzer = SimpleAnalyzer

class PileAnalysisProcessor:
    """桩基分析处理器"""
    
    def __init__(self, pile_id: str = None):
        self.pile_id = pile_id
        self.analyzer = GZTraditionalAnalyzer()
        self.processed_data = None
        self.analysis_results = None
        
    def load_data_from_pile_analysis(self) -> bool:
        """加载处理后的数据，优先使用process_data.py生成的文件"""
        print(f"[加载] 加载桩基分析数据...")

        try:
            # 优先尝试直接读取process_data.py生成的txt文件
            print(f"[尝试] 查找process_data.py生成的_processed.txt文件...")
            if self._load_from_txt_files():
                return True

            # 备用方案：从pile_analysis.py获取处理后的数据（如果可用）
            if PILE_ANALYSIS_AVAILABLE:
                print(f"[备用] 尝试从pile_analysis.py加载数据...")
                self.processed_data = get_processed_data(self.pile_id)

                if self.processed_data:
                    print(f"[成功] 从pile_analysis.py找到 {len(self.processed_data)} 个数据文件")
                    return self._convert_to_analyzer_format()

            print(f"[失败] 未找到任何可用的数据源")
            return False

        except Exception as e:
            print(f"[错误] 数据加载失败: {str(e)}")
            return False

    def _select_best_matching_file(self, txt_files: list) -> str:
        """
        从文件列表中选择最佳匹配的文件
        优先级：
        1. 精确匹配桩号的文件
        2. 包含桩号的文件
        3. 第一个文件（向后兼容）
        """
        if not txt_files:
            return None

        if not self.pile_id:
            print(f"[选择] 无桩号信息，使用第一个文件: {txt_files[0]}")
            return txt_files[0]

        # 优先级1：精确匹配桩号的文件 (桩号_processed.txt)
        exact_match_pattern = f"{self.pile_id}_processed.txt"
        for file in txt_files:
            if file.endswith(exact_match_pattern):
                print(f"[选择] 找到精确匹配文件: {file}")
                return file

        # 优先级2：包含桩号的文件
        for file in txt_files:
            if self.pile_id in file:
                print(f"[选择] 找到包含桩号的文件: {file}")
                return file

        # 优先级3：第一个文件（向后兼容）
        print(f"[警告] 未找到匹配桩号 {self.pile_id} 的文件，使用第一个文件: {txt_files[0]}")
        return txt_files[0]

    def _load_from_txt_files(self) -> bool:
        """直接从txt文件加载数据，优先处理process_data.py生成的文件"""
        import glob

        # 优先查找process_data.py生成的_processed.txt文件
        processed_patterns = [
            f"*{self.pile_id}*_processed.txt" if self.pile_id else "*_processed.txt",
            f"*_processed.txt"  # 通用模式
        ]

        # 备用文件模式
        backup_patterns = [
            f"*{self.pile_id}*_with_energy.txt",
            f"*{self.pile_id}*_waveform_processed.txt"
        ] if self.pile_id else []

        txt_files = []

        # 首先查找processed文件
        for pattern in processed_patterns:
            files = glob.glob(pattern)
            txt_files.extend(files)

        # 如果没找到，查找备用文件
        if not txt_files:
            for pattern in backup_patterns:
                files = glob.glob(pattern)
                txt_files.extend(files)

        if not txt_files:
            print(f"[错误] 未找到txt数据文件")
            print(f"[提示] 请确保存在*_processed.txt文件（由process_data.py生成）")
            return False

        # 修复文件选择逻辑：优先选择精确匹配桩号的文件
        txt_file = self._select_best_matching_file(txt_files)
        print(f"[读取] 使用txt文件: {txt_file}")

        # 检查是否为process_data.py生成的标准化文件
        is_normalized_file = "_processed.txt" in txt_file
        if is_normalized_file:
            print(f"[检测] 发现标准化能量数据文件，将使用适配的分析标准")

        try:
            # 读取txt文件
            data = []
            profile_names = []  # 存储实际的剖面名称

            with open(txt_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 解析标题行以获取实际的剖面名称
            if lines:
                header = lines[0].strip().split('\t')
                # 从标题行提取剖面名称 (格式如: "1-2 Speed%", "1-3 Speed%", "2-3 Speed%")
                for col_name in header[1:]:  # 跳过Depth列
                    if 'Speed%' in col_name:
                        # 提取剖面名称 (如从"1-2 Speed%"提取"1-2")
                        profile_name = col_name.replace(' Speed%', '').strip()
                        profile_names.append(profile_name)

                print(f"[检测] 发现 {len(profile_names)} 个剖面: {', '.join(profile_names)}")

            # 跳过标题行，处理数据行
            for line in lines[1:]:
                if line.strip():
                    parts = line.strip().split('\t')
                    if len(parts) >= 1 + len(profile_names) * 4:  # 确保有足够的列
                        try:
                            row = {
                                'Depth': float(parts[0]),
                                'is_normalized': is_normalized_file,  # 标记是否为标准化数据
                                'profile_names': profile_names  # 存储实际的剖面名称
                            }

                            # 根据实际检测到的剖面数量解析数据
                            for profile_idx, profile_name in enumerate(profile_names):
                                # 计算当前剖面的基础索引
                                base_index = 1 + profile_idx * 4

                                # 解析当前剖面的所有指标
                                try:
                                    row[f'S{profile_idx + 1}'] = float(parts[base_index])     # Speed%
                                    row[f'A{profile_idx + 1}'] = float(parts[base_index + 1]) # Amp
                                    row[f'E{profile_idx + 1}'] = float(parts[base_index + 2]) # Energy%
                                    row[f'P{profile_idx + 1}'] = float(parts[base_index + 3]) # PSD

                                    # 同时存储带剖面名称的数据用于调试
                                    row[f'{profile_name}_speed'] = float(parts[base_index])
                                    row[f'{profile_name}_amp'] = float(parts[base_index + 1])
                                    row[f'{profile_name}_energy'] = float(parts[base_index + 2])
                                    row[f'{profile_name}_psd'] = float(parts[base_index + 3])
                                except (ValueError, IndexError) as e:
                                    print(f"[警告] 解析剖面 {profile_name} 数据失败: {e}")
                                    break
                            data.append(row)
                        except (ValueError, IndexError):
                            continue

            if not data:
                print(f"[错误] txt文件中没有有效数据")
                return False

            # 创建DataFrame
            df = pd.DataFrame(data)
            print(f"[成功] 从txt文件读取 {len(data)} 个测点")

            # 同时填充processed_data结构以支持深度范围计算
            self.processed_data = {
                txt_file: {
                    'data_rows': [{'depth': row['Depth']} for row in data]
                }
            }
            print(f"[调试] 已填充processed_data结构，包含 {len(data)} 个深度数据点")

            # 存储原始数据供后续过滤使用
            self.original_dataframe = df.copy()
            print(f"[调试] 已保存原始数据框，包含 {len(df)} 个数据点")

            # 自动深度范围限制：排除表层0.7m和底部0.7m，仅用于完整性判定阶段
            try:
                if 'Depth' in df.columns and not df.empty:
                    max_depth = float(df['Depth'].max())
                    start_depth = 0.60
                    end_depth = max_depth - 0.50
                    if end_depth <= start_depth:
                        print(f"[深度过滤] 最大深度过小，无法应用0.7m边界过滤 (max_depth={max_depth:.2f}m)")
                        filtered_df = df
                    else:
                        filtered_df = df[(df['Depth'] >= start_depth) & (df['Depth'] <= end_depth)].copy()
                        print(f"[深度过滤] 判定有效范围: {start_depth:.2f}m - {end_depth:.2f}m (最大深度 {max_depth:.2f}m)")
                        print(f"[深度过滤] 数据点: {len(df)} → {len(filtered_df)} (仅用于完整性判定)")
                else:
                    filtered_df = df
            except Exception as e:
                print(f"[警告] 自动深度过滤失败，使用全部数据: {e}")
                filtered_df = df

            # 如果是标准化文件，调整分析器的能量判断标准
            if is_normalized_file:
                self._adjust_analyzer_for_normalized_data()

            # 仅将过滤后的数据用于分析器加载（保持速度/幅度/能量计算不变）
            return self.analyzer.load_data_from_dataframe(filtered_df)

        except Exception as e:
            print(f"[错误] 读取txt文件失败: {str(e)}")
            return False

    def _adjust_analyzer_for_normalized_data(self):
        """为标准化数据调整分析器的判断标准"""
        print(f"[调整] 为标准化能量数据调整分析标准")

        # 调整能量判断标准以适应标准化数据（0-1范围）
        # 基于实际数据分布优化的标准：大部分数据在0.6-1.0范围内
        # 优化后的标准：更适合I类桩的判定要求
        if hasattr(self.analyzer, 'energy_thresholds'):
            self.analyzer.energy_thresholds = {
                1: 0.6,   # ≥0.6 为完整 (降低阈值以适应实际数据分布)
                2: 0.4,   # 0.4-0.6 为轻微缺陷
                3: 0.2,   # 0.2-0.4 为明显缺陷
                4: 0.0    # <0.2 为严重缺陷
            }
            print(f"[调整] 能量判断标准已优化为I类桩适配范围: I=1(≥0.6), I=2(0.4-0.6), I=3(0.2-0.4), I=4(<0.2)")

        # 设置标记，表明使用的是标准化数据
        self.analyzer.using_normalized_energy = True
    
    def _convert_to_analyzer_format(self) -> bool:
        """将数据转换为分析器可用的格式"""
        try:
            # 合并所有文件的数据
            all_data = []
            
            for file_name, file_data in self.processed_data.items():
                print(f"[处理] 转换文件: {file_name}")
                
                for row in file_data['data_rows']:
                    # 深度范围过滤 - 仅处理指定深度范围内的数据点
                    current_depth = row['depth']
                    if hasattr(self, 'analysis_depth_range') and self.analysis_depth_range:
                        start_depth, end_depth = self.analysis_depth_range
                        if current_depth < start_depth or current_depth > end_depth:
                            print(f"[深度过滤] 跳过深度 {current_depth:.2f}m (范围外: {start_depth:.2f}m - {end_depth:.2f}m)")
                            continue  # 跳过不在分析范围内的数据点
                        else:
                            print(f"[深度过滤] 包含深度 {current_depth:.2f}m (范围内: {start_depth:.2f}m - {end_depth:.2f}m)")

                    # 转换为GUI分析器期望的格式
                    # 动态构建所有剖面的数据
                    converted_row = {'Depth': current_depth}
                    profile_count = 1
                    
                    # 支持的剖面名称列表（按行业标准顺序）
                    profile_names = ['1-2', '1-3', '1-4', '2-3', '2-4', '3-4', '2-5', '3-5', '4-5']
                    
                    for profile in profile_names:
                        # 将剖面名称转换为列名格式（如1-2 -> 12）
                        col_suffix = profile.replace('-', '')
                        
                        # 检查该剖面是否存在数据
                        speed_key = f'speed_pct_{col_suffix}'
                        if speed_key not in row:
                            continue
                        
                        # 添加该剖面的所有指标
                        converted_row[f'S{profile_count}'] = row[f'speed_pct_{col_suffix}']
                        converted_row[f'A{profile_count}'] = row[f'amp_pct_{col_suffix}']
                        converted_row[f'E{profile_count}'] = row[f'energy_{col_suffix}']
                        converted_row[f'P{profile_count}'] = row[f'psd_{col_suffix}']
                        
                        profile_count += 1
                    all_data.append(converted_row)
            
            # 创建DataFrame
            df = pd.DataFrame(all_data)
            
            # 按深度排序
            df = df.sort_values('Depth').reset_index(drop=True)

            # 显示深度范围和数据点信息
            if hasattr(self, 'analysis_depth_range') and self.analysis_depth_range:
                start_depth, end_depth = self.analysis_depth_range
                print(f"[深度过滤] 分析范围: {start_depth:.2f}m - {end_depth:.2f}m")
                print(f"[转换] 数据转换完成: {df.shape[0]} 个测点 (已过滤)")
                print(f"[调试] 过滤后深度范围: {df['Depth'].min():.2f}m - {df['Depth'].max():.2f}m")
            else:
                print(f"[转换] 数据转换完成: {df.shape[0]} 个测点 (全部范围)")
                print(f"[调试] 全部深度范围: {df['Depth'].min():.2f}m - {df['Depth'].max():.2f}m")
            
            # 加载到分析器
            return self.analyzer.load_data_from_dataframe(df)
            
        except Exception as e:
            print(f"[错误] 数据转换失败: {str(e)}")
            return False
    
    def run_analysis(self) -> bool:
        """运行GZ传统分析"""
        print(f"[分析] 开始GZ传统分析...")
        
        try:
            self.analysis_results = self.analyzer.run_analysis()
            
            if self.analysis_results:
                print(f"[成功] 分析完成: {self.analysis_results.get('final_category', 'N/A')}")
                return True
            else:
                print(f"[失败] 分析执行失败")
                return False
                
        except Exception as e:
            print(f"[错误] 分析失败: {str(e)}")
            return False
    
    def generate_txt_report(self, output_file: str = None) -> str:
        """生成txt格式的分析报告，支持标准化数据"""
        if not self.analysis_results:
            print(f"[错误] 没有分析结果可生成报告")
            return None

        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if self.pile_id:
                output_file = f"{self.pile_id}_integrity_analysis_{timestamp}.txt"
            else:
                output_file = f"pile_integrity_analysis_{timestamp}.txt"

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                # 标题 - 按照用户要求的格式
                final_category = self.analysis_results.get('final_category', 'N/A')
                f.write(f"桩基完整性类别: {final_category}\n\n")
                f.write("GZ方法桩基完整性分析结果\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"最终判定: {final_category}\n\n")

                # 分析配置
                f.write("分析配置:\n")
                enabled_indicators = self.analysis_results.get('enabled_indicators', {})
                enabled_list = []
                if enabled_indicators.get('speed', False):
                    enabled_list.append('声速')
                if enabled_indicators.get('amplitude', False):
                    enabled_list.append('波幅')
                if enabled_indicators.get('energy', False):
                    enabled_list.append('能量')
                if enabled_indicators.get('psd', False):
                    enabled_list.append('PSD')

                gz_depth_range = self.analysis_results.get('gz_depth_range', 0.5)
                f.write(f"- K值计算深度范围: 已启用 ({gz_depth_range}m / {int(gz_depth_range*100)}cm)\n")
                f.write(f"- 启用指标: {', '.join(enabled_list)}\n")

                # 深度分析范围信息
                if hasattr(self, 'analysis_depth_range') and self.analysis_depth_range:
                    start_depth, end_depth = self.analysis_depth_range
                    f.write(f"- 深度分析范围: {start_depth:.2f}m - {end_depth:.2f}m (用户指定)\n")
                else:
                    f.write(f"- 深度分析范围: 全部范围 (默认)\n")

                f.write("- 判定依据: 新版GZ Traditional Analysis分类规则\n\n")

                # 新版分类规则说明
                f.write("新版分类规则说明:\n")
                f.write("I类桩: 所有检测截面Ki值均为1\n")
                f.write("II类桩: 仅存在一个K=2，或多个K=2但不存在50cm连续范围\n")
                f.write("III类桩: 仅存在一个K=3，或多个K=3且距离≥50cm，或K=2存在50cm连续范围\n")
                f.write("IV类桩: 仅存在一个K=4，或K=3存在50cm连续范围\n\n")

                # I值计算方法详细说明
                f.write("I(j,i)值计算方法说明 (2025年最新标准):\n")
                f.write("=" * 50 + "\n")
                f.write("I(j,i) = 1 (完整)\n")
                f.write("● 定性描述：声速和波幅参数均无异常，实测波形正常；或声速或波幅存在轻微异常，实测波形畸变不明显。\n")
                f.write("● 量化条件：满足下列条件之一\n")
                f.write("  ○ 相对声速(V%) > 100%，且波幅差(Amp) ≤ 0dB，且归一化能量(E%) > 0.8。\n")
                f.write("  ○ 85% < 相对声速(V%) ≤ 100%，且波幅差(Amp) ≤ 0dB，且归一化能量(E%) > 0.8。\n")
                f.write("  ○ 相对声速(V%) > 100%，且波幅差(Amp) ≤ 4dB，且归一化能量(E%) > 0.8。\n\n")

                f.write("I(j,i) = 2 (轻微缺陷)\n")
                f.write("● 定性描述：声速和波幅参数均存在轻微异常，实测波形畸变较明显；或声速或波幅存在较明显异常，实测波形畸变较明显。\n")
                f.write("● 量化条件：满足下列条件之一\n")
                f.write("  ○ 85% < 相对声速(V%)，且波幅差(Amp) ≤ 4dB，且 0.5 < 归一化能量(E%) ≤ 0.8。\n")
                f.write("  ○ 75% < 相对声速(V%) ≤ 85%，且波幅差(Amp) ≤ 4dB，且归一化能量(E%) > 0.5。\n")
                f.write("  ○ 相对声速(V%) > 85%，且 4dB < 波幅差(Amp) ≤ 8dB，且归一化能量(E%) > 0.5。\n\n")

                f.write("I(j,i) = 3 (显著缺陷)\n")
                f.write("● 定性描述：声速和波幅参数均存在较明显异常，实测波形畸变明显；或声速或波幅存在明显异常，实测波形畸变明显。\n")
                f.write("● 量化条件：满足下列条件之一\n")
                f.write("  ○ 75% < 相对声速(V%) ≤ 85%，且 4dB < 波幅差(Amp) ≤ 8dB，且 0.25 < 归一化能量(E%) ≤ 0.5。\n")
                f.write("  ○ 65% < 相对声速(V%) ≤ 75%，且 8dB ≤ 波幅差(Amp) ≤ 12dB，且归一化能量(E%) > 0.25。\n")
                f.write("  ○ 相对声速(V%) > 75%，且 8dB < 波幅差(Amp) ≤ 12dB，且归一化能量(E%) > 0.25。\n\n")

                f.write("I(j,i) = 4 (严重缺陷)\n")
                f.write("● 定性描述：声速和波幅参数均存在明显异常，实测波形畸变严重；或声速或波幅存在严重异常，实测波形畸变严重。\n")
                f.write("● 量化条件：满足下列条件之一\n")
                f.write("  ○ 65% < 相对声速(V%) ≤ 75%，且 8dB < 波幅差(Amp) ≤ 12dB，且归一化能量(E%) ≤ 0.25。\n")
                f.write("  ○ 相对声速(V%) ≤ 65%，且波幅差(Amp) ≤ 12dB，且归一化能量(E%) ≤ 0.25。\n")
                f.write("  ○ 相对声速(V%) > 65%，且波幅差(Amp) > 12dB，且归一化能量(E%) ≤ 0.25。\n\n")
                f.write("=" * 50 + "\n\n")

                # 判定依据
                f.write("判定依据:\n")
                report_details = self.analysis_results.get('report_details', [])
                for detail in report_details:
                    f.write(f"- {detail}\n")
                f.write("\n")

                # K值分布统计
                k_values = list(self.analysis_results.get('K_values', {}).values())
                if k_values:
                    f.write("K值分布统计:\n")
                    for k in [1, 2, 3, 4]:
                        count = k_values.count(k)
                        percentage = (count / len(k_values)) * 100 if len(k_values) > 0 else 0
                        if count > 0:
                            f.write(f"K={k}: {count}个截面 ({percentage:.1f}%)\n")
                    f.write("\n")

                # 总计分析截面
                f.write(f"总计分析截面: {len(k_values)}个\n")
                f.write("\n")

                # 详细分析结果
                f.write("详细分析结果:\n")
                f.write("-" * 50 + "\n")

                # 写入每个深度的详细分析
                self._write_detailed_analysis_results(f)

            
            print(f"[成功] 分析报告已保存: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"[错误] 生成报告失败: {str(e)}")
            return None

    def _write_detailed_k_calculation(self, f):
        """写入详细的K值计算过程"""
        f.write("详细K值计算过程:\n")
        f.write("=" * 80 + "\n")
        f.write("K值计算公式: K(i) = int[(∑I(j,i)² / ∑I(j,i)) + 0.5]\n")
        f.write("其中：I(j,i)为各剖面在深度i处的完整性指标值\n\n")

        # 获取详细计算数据
        k_values_map = self.analysis_results.get('K_values', {})
        k_calculation_details = self.analysis_results.get('K_calculation_details', {})
        I_ji_values = self.analysis_results.get('I_ji_values', {})
        raw_data = self.analysis_results.get('raw_data', {})
        enabled_indicators = self.analysis_results.get('enabled_indicators', {})

        for depth in sorted(k_values_map.keys()):
            f.write(f"{'='*80}\n")
            f.write(f"深度 {depth:.2f}m 的K值计算详细过程\n")
            f.write(f"{'='*80}\n")
            f.write(f"最终K值: {k_values_map[depth]}\n\n")

            # 步骤1: 各剖面I(j,i)值计算
            if depth in I_ji_values:
                f.write(f"步骤1: 各剖面I(j,i)值计算\n")
                f.write(f"{'-'*50}\n")

                for profile, I_ji in I_ji_values[depth].items():
                    f.write(f"剖面{profile}: I(j,i) = {I_ji}")

                    # 添加I(j,i)取值原因说明
                    if depth in raw_data and profile in raw_data[depth]:
                        data = raw_data[depth][profile]
                        reason = self._get_I_ji_reason(I_ji, data, enabled_indicators)
                        f.write(f" ({reason})")

                    f.write("\n")
                f.write("\n")

            # 步骤2: K值计算过程
            if depth in k_calculation_details:
                calc_detail = k_calculation_details[depth]
                f.write(f"步骤2: K值计算过程\n")
                f.write(f"{'-'*50}\n")
                f.write(f"计算方法: {'深度范围模式' if calc_detail['method'] == 'depth_range' else '当前深度模式'}\n")

                if calc_detail['method'] == 'depth_range':
                    f.write(f"深度范围: ±{calc_detail['depth_range']/2.0:.2f}m\n")
                    f.write(f"参与计算的深度: {[f'{d:.2f}m' for d in calc_detail['depths_used']]}\n")
                else:
                    f.write(f"仅使用当前深度: {depth:.2f}m\n")

                detail = calc_detail['calculation_detail']
                I_ji_list = detail['valid_I_ji']

                if I_ji_list:
                    f.write(f"\n计算公式: K(i) = int[(∑I(j,i)² / ∑I(j,i)) + 0.5]\n")
                    f.write(f"有效I(j,i)值: {I_ji_list}\n\n")

                    # 详细计算步骤
                    f.write(f"详细计算步骤:\n")
                    f.write(f"  ① 计算∑I(j,i)²:\n")
                    # 使用有效的I(j,i)值进行计算显示
                    valid_I_ji = detail['valid_I_ji']
                    if valid_I_ji:
                        squares_str = ' + '.join([f'{i}²' for i in valid_I_ji])
                        squares_values_str = ' + '.join([str(i**2) for i in valid_I_ji])
                        f.write(f"     ∑I(j,i)² = {squares_str} = {squares_values_str} = {detail['sum_I_ji_sq']}")
                    else:
                        f.write(f"     ∑I(j,i)² = 0 (无有效I(j,i)值)")

                    # 显示N/A值信息
                    if detail.get('na_count', 0) > 0:
                        f.write(f" (排除{detail['na_count']}个N/A值)")
                    f.write("\n\n")

                    f.write(f"  ② 计算∑I(j,i):\n")
                    if valid_I_ji:
                        sum_str = ' + '.join([str(i) for i in valid_I_ji])
                        f.write(f"     ∑I(j,i) = {sum_str} = {detail['sum_I_ji']}")
                    else:
                        f.write(f"     ∑I(j,i) = 0 (无有效I(j,i)值)")

                    # 显示N/A值信息
                    if detail.get('na_count', 0) > 0:
                        f.write(f" (排除{detail['na_count']}个N/A值)")
                    f.write("\n\n")

                    f.write(f"  ③ 计算比值:\n")
                    f.write(f"     ∑I(j,i)² / ∑I(j,i) = {detail['sum_I_ji_sq']} / {detail['sum_I_ji']} = {detail['ratio']:.4f}\n\n")

                    f.write(f"  ④ 加0.5:\n")
                    f.write(f"     {detail['ratio']:.4f} + 0.5 = {detail['K_i_float']:.4f}\n\n")

                    f.write(f"  ⑤ 取整得到最终K值:\n")
                    f.write(f"     K(i) = int({detail['K_i_float']:.4f}) = {detail['final_K']}\n\n")

                    # 添加验证信息
                    f.write(f"步骤3: 结果验证\n")
                    f.write(f"{'-'*50}\n")
                    f.write(f"计算结果: K(i) = {detail['final_K']}\n")
                    f.write(f"验证状态: {detail['note']}\n")
                    if detail['final_K'] == k_values_map[depth]:
                        f.write(f"✓ 计算结果与存储值一致\n")
                    else:
                        f.write(f"✗ 计算结果与存储值不一致，请检查\n")
                else:
                    f.write(f"计算状态: {detail['note']}\n")

            f.write("\n")

        f.write("\n")

    def _get_I_ji_reason(self, I_ji_value, data, enabled_indicators):
        """获取I(j,i)值的取值原因说明"""
        try:
            speed = data.get('speed', None)
            amplitude = data.get('amplitude', None)
            energy = data.get('energy', None)
            psd = data.get('psd', None)

            reasons = []

            # 根据I(j,i)值分析原因，只显示启用的指标
            if I_ji_value == 1:
                if enabled_indicators.get('speed', False) and speed is not None and 100.0 <= speed <= 1000.0:
                    reasons.append(f"声速{speed:.1f}%≥100%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and -100 <= amplitude <= 4:
                    reasons.append(f"波幅{amplitude:.1f}dB≤4dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0.8 <= energy <= 100:
                    reasons.append(f"能量{energy:.2f}≥0.8")
                if enabled_indicators.get('psd', False) and psd is not None and 0 <= psd <= 1:
                    reasons.append(f"PSD{psd:.2f}≤1")

            elif I_ji_value == 2:
                if enabled_indicators.get('speed', False) and speed is not None and 85.0 <= speed < 100.0:
                    reasons.append(f"声速{speed:.1f}%在85-100%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and 4 < amplitude <= 8:
                    reasons.append(f"波幅{amplitude:.1f}dB在4-8dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0.5 <= energy < 0.8:
                    reasons.append(f"能量{energy:.2f}在0.5-0.8")
                if enabled_indicators.get('psd', False) and psd is not None and 1 < psd <= 2:
                    reasons.append(f"PSD{psd:.2f}在1-2")

            elif I_ji_value == 3:
                if enabled_indicators.get('speed', False) and speed is not None and 75.0 <= speed < 85.0:
                    reasons.append(f"声速{speed:.1f}%在75-85%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and 8 < amplitude <= 12:
                    reasons.append(f"波幅{amplitude:.1f}dB在8-12dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0.20 <= energy < 0.5:
                    reasons.append(f"能量{energy:.2f}在0.20-0.5")
                if enabled_indicators.get('psd', False) and psd is not None and 2 < psd <= 3:
                    reasons.append(f"PSD{psd:.2f}在2-3")

            elif I_ji_value == 4:
                if enabled_indicators.get('speed', False) and speed is not None and 65.0 <= speed < 75.0:
                    reasons.append(f"声速{speed:.1f}%在65-75%")
                elif enabled_indicators.get('speed', False) and speed is not None and speed < 65.0:
                    reasons.append(f"声速{speed:.1f}%<65%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and amplitude > 12:
                    reasons.append(f"波幅{amplitude:.1f}dB>12dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0 <= energy < 0.20:
                    reasons.append(f"能量{energy:.2f}<0.20")
                if enabled_indicators.get('psd', False) and psd is not None and psd > 3:
                    reasons.append(f"PSD{psd:.2f}>3")

            if reasons:
                return "，".join(reasons)
            else:
                # 如果没有找到具体原因，显示启用指标的原始数据
                data_parts = []
                if enabled_indicators.get('speed', False) and speed is not None:
                    data_parts.append(f"声速{speed:.1f}%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None:
                    data_parts.append(f"波幅{amplitude:.1f}dB")
                if enabled_indicators.get('energy', False) and energy is not None:
                    data_parts.append(f"能量{energy:.2f}")
                if enabled_indicators.get('psd', False) and psd is not None:
                    data_parts.append(f"PSD{psd:.2f}")
                return "，".join(data_parts) if data_parts else "数据不完整"

        except Exception:
            return "计算原因获取失败"

    def _get_detailed_I_ji_reason(self, I_ji_value, data, enabled_indicators):
        """获取详细的I(j,i)值原因说明 - 按照2025年最新标准的详细格式"""
        try:
            speed = data.get('speed', None)
            amplitude = data.get('amplitude', None)
            energy = data.get('energy', None)

            # 根据2025年最新标准提供详细的判定说明
            if I_ji_value == 1:
                # I=1 (完整): 检查满足的具体条件
                reasons = []

                # 条件1: 相对声速(V%) > 100%，且波幅差(Amp) ≤ 0dB，且归一化能量(E%) > 0.8
                if (speed is not None and speed > 100 and
                    amplitude is not None and amplitude <= 0 and
                    energy is not None and energy > 0.8):
                    reasons.append(f"满足条件1：声速{speed:.1f}%>100%，波幅{amplitude:.1f}dB≤0dB，能量{energy:.2f}>0.8")

                # 条件2: 85% < 相对声速(V%) ≤ 100%，且波幅差(Amp) ≤ 0dB，且归一化能量(E%) > 0.8
                elif (speed is not None and 85 < speed <= 100 and
                      amplitude is not None and amplitude <= 0 and
                      energy is not None and energy > 0.8):
                    reasons.append(f"满足条件2：声速{speed:.1f}%在85%-100%，波幅{amplitude:.1f}dB≤0dB，能量{energy:.2f}>0.8")

                # 条件3: 相对声速(V%) > 100%，且波幅差(Amp) ≤ 4dB，且归一化能量(E%) > 0.8
                elif (speed is not None and speed > 100 and
                      amplitude is not None and amplitude <= 4 and
                      energy is not None and energy > 0.8):
                    reasons.append(f"满足条件3：声速{speed:.1f}%>100%，波幅{amplitude:.1f}dB≤4dB，能量{energy:.2f}>0.8")
                
                # [二次修复] 对应新增的判定条件4
                elif (speed is not None and 85 < speed <= 100 and
                      amplitude is not None and amplitude <= 0 and
                      energy is not None and energy >= 0.7):
                    reasons.append(f"满足新增条件4：声速{speed:.1f}%>85%，波幅{amplitude:.1f}dB≤0dB，能量{energy:.2f}≥0.7")

                # 如果没有明确匹配条件，显示主要指标
                else:
                    parts = []
                    if speed is not None and speed >= 100:
                        parts.append(f"声速{speed:.1f}%≥100%")
                    if amplitude is not None and amplitude <= 4:
                        parts.append(f"波幅{amplitude:.1f}dB≤4dB")
                    if energy is not None and energy >= 0.8:
                        parts.append(f"能量{energy:.2f}≥0.8")
                    if parts:
                        reasons.append("，".join(parts))

                return reasons[0] if reasons else "声速和波幅参数均无异常，实测波形正常"

            elif I_ji_value == 2:
                # I=2 (轻微缺陷): 检查满足的具体条件
                # 条件1: 85% < 相对声速(V%)，且波幅差(Amp) ≤ 4dB，且 0.5 < 归一化能量(E%) ≤ 0.8
                if (speed is not None and speed > 85 and
                    amplitude is not None and amplitude <= 4 and
                    energy is not None and 0.5 < energy <= 0.8):
                    return f"满足条件1：声速{speed:.1f}%>85%，波幅{amplitude:.1f}dB≤4dB，能量{energy:.2f}在0.5-0.8"

                # 条件2: 75% < 相对声速(V%) ≤ 85%，且波幅差(Amp) ≤ 4dB，且归一化能量(E%) > 0.5
                elif (speed is not None and 75 < speed <= 85 and
                      amplitude is not None and amplitude <= 4 and
                      energy is not None and energy > 0.5):
                    return f"满足条件2：声速{speed:.1f}%在75%-85%，波幅{amplitude:.1f}dB≤4dB，能量{energy:.2f}>0.5"

                # 条件3: 相对声速(V%) > 85%，且 4dB < 波幅差(Amp) ≤ 8dB，且归一化能量(E%) > 0.5
                elif (speed is not None and speed > 85 and
                      amplitude is not None and 4 < amplitude <= 8 and
                      energy is not None and energy > 0.5):
                    return f"满足条件3：声速{speed:.1f}%>85%，波幅{amplitude:.1f}dB在4-8dB，能量{energy:.2f}>0.5"

                # 条件5: 85% < 相对声速(V%) < 100%，且 0 < 波幅差(Amp) ≤ 4dB，且归一化能量(E%) > 0.8
                elif (speed is not None and 85 < speed < 100 and
                      amplitude is not None and 0 < amplitude <= 4 and
                      energy is not None and energy > 0.8):
                    return f"满足条件5：声速{speed:.1f}%在85%-100%，波幅{amplitude:.1f}dB在0-4dB，能量{energy:.2f}>0.8"

                # 简化显示
                else:
                    if energy is not None and 0.5 <= energy < 0.8:
                        return f"能量{energy:.2f}在0.5-0.8"
                    elif speed is not None and 85 <= speed < 100:
                        return f"声速{speed:.1f}%在85-100%"
                    elif amplitude is not None and 4 < amplitude <= 8:
                        return f"波幅{amplitude:.1f}dB在4-8dB"
                    else:
                        return "声速和波幅参数均存在轻微异常"

            elif I_ji_value == 3:
                # I=3 (显著缺陷): 检查满足的具体条件 - 更新为新的分层判定标准
                # 条件1: 75% < 相对声速(V%) ≤ 85%，且 4dB < 波幅差(Amp) ≤ 8dB，且 0.25 < 归一化能量(E%) < 0.5
                if (speed is not None and 75 < speed <= 85 and
                    amplitude is not None and 4 < amplitude <= 8 and
                    energy is not None and 0.25 < energy < 0.5):
                    return f"满足条件1：声速{speed:.1f}%在75%-85%，波幅{amplitude:.1f}dB在4-8dB，能量{energy:.2f}在0.25-0.5"

                # 条件2: 65% < 相对声速(V%) ≤ 75%，且波幅差(Amp) ≤ 12dB，且归一化能量(E%) ≥ 0.25
                elif (speed is not None and 65 < speed <= 75 and
                      amplitude is not None and amplitude <= 12 and
                      energy is not None and energy >= 0.25):
                    return f"满足条件2：声速{speed:.1f}%在65%-75%，波幅{amplitude:.1f}dB≤12dB，能量{energy:.2f}≥0.25"

                # 条件3: 相对声速(V%) > 65%，且 8dB < 波幅差(Amp) ≤ 12dB，且归一化能量(E%) ≥ 0.25
                elif (speed is not None and speed > 65 and
                      amplitude is not None and 8 < amplitude <= 12 and
                      energy is not None and energy >= 0.25):
                    return f"满足条件3：声速{speed:.1f}%>65%，波幅{amplitude:.1f}dB在8-12dB，能量{energy:.2f}≥0.25"

                # 简化显示
                else:
                    if energy is not None and 0.20 <= energy < 0.5:
                        return f"能量{energy:.2f}在0.20-0.5"
                    elif speed is not None and 75 <= speed < 85:
                        return f"声速{speed:.1f}%在75-85%"
                    elif amplitude is not None and 8 < amplitude <= 12:
                        return f"波幅{amplitude:.1f}dB在8-12dB"
                    else:
                        return "声速和波幅参数均存在较明显异常"

            elif I_ji_value == 4:
                # I=4 (严重缺陷): 检查满足的具体条件 - 更新为新的分层判定标准
                # 严重条件1: 相对声速(V%) ≤ 65%
                if (speed is not None and speed <= 65):
                    return f"满足严重条件1：声速{speed:.1f}%≤65% (严重缺陷)"

                # 严重条件2: 波幅差(Amp) > 12dB
                elif (amplitude is not None and amplitude > 12):
                    return f"满足严重条件2：波幅{amplitude:.1f}dB>12dB (严重缺陷)"

                # 严重条件3: 归一化能量(E%) < 0.25
                elif (energy is not None and energy < 0.25):
                    return f"满足严重条件3：能量{energy:.2f}<0.25 (严重缺陷)"

                # 简化显示
                else:
                    if energy is not None and energy < 0.20:
                        return f"能量{energy:.2f}<0.20"
                    elif speed is not None and speed < 75:
                        return f"声速{speed:.1f}%<75%"
                    elif amplitude is not None and amplitude > 12:
                        return f"波幅{amplitude:.1f}dB>12dB"
                    else:
                        return "声速和波幅参数均存在明显异常"

            # 如果没有找到具体原因，返回默认格式
            return f"能量{energy:.2f}" if energy is not None else "数据不完整"

        except Exception:
            return "计算原因获取失败"

    def _write_detailed_analysis_results(self, f):
        """写入详细分析结果 - 增强格式显示实测参数和条件判定"""
        k_values_map = self.analysis_results.get('K_values', {})
        I_ji_values = self.analysis_results.get('I_ji_values', {})
        raw_data = self.analysis_results.get('raw_data', {})
        enabled_indicators = self.analysis_results.get('enabled_indicators', {})
        k_calculation_details = self.analysis_results.get('K_calculation_details', {})

        for depth in sorted(k_values_map.keys()):
            k_val = k_values_map[depth]
            f.write(f"深度 {depth:.2f}m: K(i) = {k_val}\n")

            # 写入各剖面的I(j,i)值和详细参数分析
            if depth in I_ji_values:
                for profile, I_ji in I_ji_values[depth].items():
                    f.write(f"  剖面{profile}: I(j,i) = {I_ji}\n")

                    # 添加增强的参数显示和条件判定
                    if depth in raw_data and profile in raw_data[depth]:
                        data = raw_data[depth][profile]
                        self._write_enhanced_parameter_analysis(f, I_ji, data, enabled_indicators)

                    f.write("")

            # 写入K值计算过程 - 按照用户要求的详细格式
            f.write("  K值计算过程:\n")
            f.write("    公式: K(i) = int[(∑I(j,i)² / ∑I(j,i)) + 0.5]\n")

            if depth in k_calculation_details:
                calc_detail = k_calculation_details[depth]
                detail = calc_detail['calculation_detail']
                I_ji_list = detail['valid_I_ji']

                if I_ji_list:
                    f.write(f"    I(j,i)值: {I_ji_list}\n")

                    # 详细计算步骤 - 按照用户要求的格式，支持N/A值
                    valid_I_ji = detail['valid_I_ji']
                    na_count = detail.get('na_count', 0)

                    if valid_I_ji:
                        squares_str = ' + '.join([f'{i}²' for i in valid_I_ji])
                        squares_values_str = ' + '.join([str(i**2) for i in valid_I_ji])
                        f.write(f"    ∑I(j,i)² = {squares_str} = {squares_values_str} = {detail['sum_I_ji_sq']}")
                        if na_count > 0:
                            f.write(f" (排除{na_count}个N/A值)")
                        f.write("\n")

                        sum_str = ' + '.join([str(i) for i in valid_I_ji])
                        f.write(f"    ∑I(j,i) = {sum_str} = {detail['sum_I_ji']}")
                        if na_count > 0:
                            f.write(f" (排除{na_count}个N/A值)")
                        f.write("\n")
                    else:
                        f.write(f"    ∑I(j,i)² = 0 (无有效I(j,i)值)\n")
                        f.write(f"    ∑I(j,i) = 0 (无有效I(j,i)值)\n")

                    f.write(f"    计算: ({detail['sum_I_ji_sq']} / {detail['sum_I_ji']}) + 0.5 = {detail['ratio']:.2f} + 0.5 = {detail['K_i_float']:.2f}\n")
                    f.write(f"    取整: int({detail['K_i_float']:.2f}) = {detail['final_K']}\n")
                else:
                    f.write(f"    计算状态: {detail['note']}\n")

            f.write("\n")

    def _write_enhanced_parameter_analysis(self, f, I_ji_value, data, enabled_indicators):
        """写入增强的参数分析 - 显示实测值、条件和判定结果"""
        try:
            speed = data.get('speed', None)
            amplitude = data.get('amplitude', None)
            energy = data.get('energy', None)

            # 显示实测参数值
            f.write("    实测参数:\n")

            if speed is not None:
                # 处理NaN值的显示
                import math
                if isinstance(speed, (int, float)) and math.isnan(speed):
                    f.write(f"      声速: 数据缺失")
                else:
                    f.write(f"      声速: {speed:.1f}%")
                # 根据I(j,i)值显示对应的条件判定
                speed_conditions = self._get_speed_condition_status(I_ji_value, speed)
                if speed_conditions:
                    f.write(f" ({speed_conditions})")
                f.write("\n")

            if amplitude is not None:
                # 处理NaN值的显示
                import math
                if isinstance(amplitude, (int, float)) and math.isnan(amplitude):
                    f.write(f"      波幅: 数据缺失")
                else:
                    f.write(f"      波幅: {amplitude:.1f}dB")
                # 根据I(j,i)值显示对应的条件判定
                amp_conditions = self._get_amplitude_condition_status(I_ji_value, amplitude)
                if amp_conditions:
                    f.write(f" ({amp_conditions})")
                f.write("\n")

            if energy is not None:
                # 处理NaN值的显示
                import math
                if isinstance(energy, (int, float)) and math.isnan(energy):
                    f.write(f"      能量: 数据缺失")
                else:
                    f.write(f"      能量: {energy:.2f}")
                # 根据I(j,i)值显示对应的条件判定
                energy_conditions = self._get_energy_condition_status(I_ji_value, energy)
                if energy_conditions:
                    f.write(f" ({energy_conditions})")
                f.write("\n")

            # 显示满足的具体条件
            reason = self._get_detailed_I_ji_reason(I_ji_value, data, enabled_indicators)
            f.write(f"    判定结果: {reason}\n")

        except Exception as e:
            # 如果增强显示失败，回退到原始格式
            reason = self._get_detailed_I_ji_reason(I_ji_value, data, enabled_indicators)
            f.write(f"    ({reason})\n")

    def _get_speed_condition_status(self, I_ji_value, speed):
        """获取声速条件状态"""
        if I_ji_value == 1:
            if speed > 100:
                return "条件: >100% ✓"
            elif speed > 85:
                return "条件: >85% ✓"
            else:
                return "条件: >85% ✗"
        elif I_ji_value == 2:
            if speed > 85:
                return "条件: >85% ✓"
            elif speed > 75:
                return "条件: >75% ✓"
            else:
                return "条件: >75% ✗"
        elif I_ji_value == 3:
            if speed > 75:
                return "条件: >75% ✓"
            elif speed > 65:
                return "条件: >65% ✓"
            else:
                return "条件: >65% ✗"
        elif I_ji_value == 4:
            if speed > 65:
                return "条件: >65% ✓"
            else:
                return "条件: ≤65% ✓"
        return ""

    def _get_amplitude_condition_status(self, I_ji_value, amplitude):
        """获取波幅条件状态"""
        if I_ji_value == 1:
            if amplitude <= 0:
                return "条件: ≤0dB ✓"
            elif amplitude <= 4:
                return "条件: ≤4dB ✓"
            else:
                return "条件: ≤4dB ✗"
        elif I_ji_value == 2:
            if amplitude <= 4:
                return "条件: ≤4dB ✓"
            elif amplitude <= 8:
                return "条件: ≤8dB ✓"
            else:
                return "条件: ≤8dB ✗"
        elif I_ji_value == 3:
            if amplitude <= 8:
                return "条件: ≤8dB ✓"
            elif amplitude <= 12:
                return "条件: ≤12dB ✓"
            else:
                return "条件: ≤12dB ✗"
        elif I_ji_value == 4:
            if amplitude <= 12:
                return "条件: ≤12dB ✓"
            else:
                return "条件: >12dB ✓"
        return ""

    def _get_energy_condition_status(self, I_ji_value, energy):
        """获取能量条件状态"""
        if I_ji_value == 1:
            if energy > 0.8:
                return "条件: >0.8 ✓"
            # [二次修复] 为新增的I=1条件4添加对应的状态显示
            elif energy >= 0.7:
                return "条件: ≥0.7 ✓"
            else:
                return "条件: ≥0.7 ✗"
        elif I_ji_value == 2:
            if 0.5 < energy <= 0.8:
                return "条件: 0.5-0.8范围内 ✓"
            elif energy > 0.5:
                return "条件: >0.5 ✓"
            else:
                return "条件: >0.5 ✗"
        elif I_ji_value == 3:
            if 0.25 < energy < 0.5:
                return "条件: 0.25<E%<0.5范围内 ✓"
            elif energy >= 0.25:
                return "条件: ≥0.25 ✓"
            else:
                return "条件: ≥0.25 ✗"
        elif I_ji_value == 4:
            if energy < 0.25:
                return "条件: <0.25 ✓"
            else:
                return "条件: <0.25 ✗"
        return ""

    def get_depth_range_from_user(self, max_depth):
        """
        获取用户指定的深度分析范围

        CONFIGURABLE DEPTH ANALYSIS RANGE FEATURE:
        允许用户指定桩基分析的深度范围，提供更精确的完整性评估

        输入格式：[起始深度, 结束偏移]
        - 起始深度: 分析开始的绝对深度值（米）
        - 结束偏移: 从最大深度的偏移量（米）
        - 最终分析范围：[起始深度, max_depth - 结束偏移]

        用户体验：
        - 空输入：分析整个深度范围（默认行为）
        - 示例输入：[0.6, 0.5] = 从0.6m到(最大深度-0.5m)
        - 输入验证：确保范围有效且为正值
        - 确认提示：显示计算的分析范围

        Args:
            max_depth (float): 数据中的最大深度值

        Returns:
            tuple: (起始深度, 结束深度) 或 None（表示分析全部范围）
        """
        try:
            print("\n" + "="*60)
            print("🎯 桩基完整性分析 - 深度范围配置")
            print("="*60)
            print(f"📊 数据深度范围: 0.0m - {max_depth:.2f}m")
            print("\n📝 深度范围输入格式说明:")
            print("   格式: [起始深度, 结束偏移]")
            print("   - 起始深度: 分析开始深度（米）")
            print("   - 结束偏移: 从最大深度的偏移量（米）")
            print("   - 最终范围: [起始深度, max_depth - 结束偏移]")
            print("\n💡 输入示例:")
            print("   [0.6, 0.5]  → 分析范围: 0.6m 到 {:.2f}m".format(max_depth - 0.5))
            print("   [1.0, 1.0]  → 分析范围: 1.0m 到 {:.2f}m".format(max_depth - 1.0))
            print("   直接回车    → 分析全部范围: 0.0m 到 {:.2f}m".format(max_depth))

            while True:
                print("\n" + "-"*50)
                import sys
                import os

                # 强制刷新输出缓冲区 - Windows兼容性改进
                sys.stdout.flush()
                sys.stderr.flush()

                # Windows控制台兼容性处理
                if os.name == 'nt':  # Windows系统
                    try:
                        # 尝试设置控制台模式以改善输入处理
                        import msvcrt
                        # 清空输入缓冲区
                        while msvcrt.kbhit():
                            msvcrt.getch()
                    except ImportError:
                        pass  # 如果msvcrt不可用，继续执行

                try:
                    # 添加明确的输入提示分隔符
                    print("\n" + "="*60)
                    print("⌨️  请在下方输入深度范围参数")
                    print("="*60)

                    # 改进的输入处理 - 解决Windows双Enter问题
                    print("请输入深度分析范围 [起始深度, 结束偏移] 或直接回车使用全部深度范围: ", end='', flush=True)
                    user_input = input().strip()

                    # 移除可能的BOM字符和额外的空白字符
                    user_input = user_input.lstrip('\ufeff').strip()

                except EOFError:
                    print("🔍 检测到EOF，自动使用全部深度范围")
                    user_input = ""
                except KeyboardInterrupt:
                    print("\n❌ 用户取消操作")
                    return None

                # 空输入：使用全部范围 - 立即处理，无需额外调试输出
                if not user_input or user_input == "":
                    print("✅ 选择: 分析全部深度范围 (0.0m - {:.2f}m)".format(max_depth))
                    print("🚀 开始全深度范围分析...")
                    return None

                # 解析输入格式 - 显示接收到的输入用于调试
                print(f"🔍 正在解析输入: '{user_input}'")
                try:
                    # 检查基本格式（保留空格用于后续解析）
                    if not (user_input.startswith("[") and user_input.endswith("]")):
                        raise ValueError("输入格式错误：必须使用 [起始深度, 结束偏移] 格式")

                    # 提取数值内容
                    content = user_input[1:-1].strip()  # 移除方括号并去除首尾空格

                    # 智能分隔符解析：支持逗号和空格分隔
                    parts = []
                    if "," in content:
                        # 逗号分隔：[0.7, 0.5] 或 [0.7,0.5]
                        parts = [part.strip() for part in content.split(",")]
                    else:
                        # 空格分隔：[0.7 0.5]
                        parts = content.split()

                    if len(parts) != 2:
                        raise ValueError("输入格式错误：必须包含两个数值，可用逗号或空格分隔")

                    start_depth = float(parts[0])
                    end_offset = float(parts[1])

                    # 计算最终范围
                    end_depth = max_depth - end_offset

                    # 验证范围有效性
                    if start_depth < 0:
                        raise ValueError(f"开始深度不能为负值: {start_depth}")
                    if end_offset < 0:
                        raise ValueError(f"结束偏移量不能为负值: {end_offset}")
                    if start_depth >= max_depth:
                        raise ValueError(f"开始深度 ({start_depth}m) 不能大于等于最大深度 ({max_depth}m)")
                    if end_depth <= start_depth:
                        raise ValueError(f"结束深度 ({end_depth:.2f}m) 必须大于开始深度 ({start_depth}m)")
                    if end_depth > max_depth:
                        raise ValueError(f"结束深度 ({end_depth:.2f}m) 不能大于最大深度 ({max_depth}m)")

                    # 显示计算的范围并自动确认 - 解决双Enter键问题
                    print(f"\n📏 计算的分析范围:")
                    print(f"   开始深度: {start_depth:.2f}m")
                    print(f"   结束深度: {end_depth:.2f}m (最大深度 {max_depth:.2f}m - 偏移量 {end_offset:.2f}m)")
                    print(f"   分析长度: {end_depth - start_depth:.2f}m")

                    # 自动确认 - 无需用户再次确认，解决双Enter键问题
                    print("✅ 深度范围配置完成")
                    print("🚀 开始指定深度范围分析...")
                    return (start_depth, end_depth)

                except ValueError as e:
                    print(f"❌ 输入错误: {e}")
                    print("💡 请检查输入格式，例如: [0.6, 0.5]")
                    continue
                except Exception as e:
                    print(f"❌ 解析错误: {e}")
                    print("💡 请使用正确的格式: [起始深度, 结束偏移]")
                    continue

        except KeyboardInterrupt:
            print("\n\n❌ 用户取消操作")
            return None
        except Exception as e:
            print(f"❌ 深度范围配置失败: {e}")
            print("🔄 将使用全部深度范围")
            return None
        finally:
            # 确保输出缓冲区被清空
            import sys
            sys.stdout.flush()
            sys.stderr.flush()

    def _apply_depth_range_filtering(self) -> bool:
        """应用深度范围过滤到已加载的数据"""
        try:
            if not hasattr(self, 'original_dataframe') or self.original_dataframe is None:
                print("[警告] 未找到原始数据框，无法应用深度范围过滤")
                return True

            if not hasattr(self, 'analysis_depth_range') or not self.analysis_depth_range:
                print("[调试] 未设置深度范围，跳过过滤")
                return True

            start_depth, end_depth = self.analysis_depth_range
            print(f"[深度过滤] 应用深度范围过滤: {start_depth:.2f}m - {end_depth:.2f}m")

            # 过滤DataFrame
            original_count = len(self.original_dataframe)
            filtered_df = self.original_dataframe[(self.original_dataframe['Depth'] >= start_depth) &
                                                  (self.original_dataframe['Depth'] <= end_depth)]
            filtered_count = len(filtered_df)

            print(f"[深度过滤] 过滤结果: {original_count} → {filtered_count} 个数据点")
            print(f"[调试] 过滤比例: {filtered_count}/{original_count} 个数据点")

            if filtered_count == 0:
                print(f"[警告] 深度范围过滤后无数据点，请检查范围设置")
                return False

            # 重新加载过滤后的数据到分析器
            print(f"[深度过滤] 重新加载过滤后的数据到分析器")
            return self.analyzer.load_data_from_dataframe(filtered_df)

        except Exception as e:
            print(f"[错误] 深度范围过滤失败: {e}")
            return False

    def run_complete_analysis(self, output_file: str = None) -> bool:
        """运行完整的分析流程"""
        print("=" * 80)
        print("桩基分析处理器 - 完整分析流程")
        print("=" * 80)
        
        # 步骤1: 加载数据
        if not self.load_data_from_pile_analysis():
            print("[失败] 数据加载失败")
            return False

        # 步骤2: 配置深度分析范围
        # 计算最大深度值
        max_depth = 0.0
        total_data_points = 0
        if self.processed_data:
            print(f"[调试] 计算最大深度，处理数据文件数: {len(self.processed_data)}")
            for file_name, file_data in self.processed_data.items():
                file_points = len(file_data['data_rows'])
                total_data_points += file_points
                print(f"[调试] 文件 {file_name}: {file_points} 个数据点")
                for row in file_data['data_rows']:
                    max_depth = max(max_depth, row['depth'])
            print(f"[调试] 总数据点: {total_data_points}, 最大深度: {max_depth:.2f}m")
        else:
            print(f"[警告] processed_data 为空，无法计算最大深度")

        depth_range = self.get_depth_range_from_user(max_depth)

        if depth_range:
            start_depth, end_depth = depth_range
            print(f"\n🎯 深度范围配置: {start_depth:.2f}m - {end_depth:.2f}m")
            print(f"[调试] 设置 analysis_depth_range = {depth_range}")
            # 存储深度范围供后续使用
            self.analysis_depth_range = depth_range

            # 应用深度范围过滤
            if not self._apply_depth_range_filtering():
                print("[失败] 深度范围过滤失败")
                return False
        else:
            print(f"\n🎯 深度范围配置: 全部范围 (0.0m - {max_depth:.2f}m)")
            print(f"[调试] 设置 analysis_depth_range = None")
            self.analysis_depth_range = None

        # 步骤3: 运行分析
        if not self.run_analysis():
            print("[失败] 分析执行失败")
            return False
        
        # 步骤4: 生成报告
        report_file = self.generate_txt_report(output_file)
        if not report_file:
            print("[失败] 报告生成失败")
            return False
        
        print("=" * 80)
        print("分析完成")
        print("=" * 80)
        return True

def process_from_process_data_files():
    """专门处理process_data.py生成的文件"""
    import glob

    print("=" * 80)
    print("桩基完整性分析处理器 - 处理process_data.py生成的文件")
    print("=" * 80)

    # 查找所有_processed.txt文件
    processed_files = glob.glob("*_processed.txt")

    if not processed_files:
        print("[错误] 未找到任何_processed.txt文件")
        print("[提示] 请先运行process_data.py生成处理后的数据文件")
        return False

    print(f"[发现] 找到 {len(processed_files)} 个处理后的数据文件:")
    for file in processed_files:
        print(f"  - {file}")
    print()

    success_count = 0
    total_count = len(processed_files)

    for file in processed_files:
        print(f"[处理] 正在分析文件: {file}")

        # 从文件名提取桩基ID
        pile_id = file.replace("_processed.txt", "")

        # 创建处理器
        processor = PileAnalysisProcessor(pile_id)

        # 运行完整分析
        if processor.run_complete_analysis():
            success_count += 1
            print(f"[成功] {file} 分析完成")
        else:
            print(f"[失败] {file} 分析失败")

        print("-" * 50)

    print(f"\n[总结] 处理完成: {success_count}/{total_count} 个文件成功分析")
    return success_count > 0

def main():
    """主函数"""
    pile_id = None

    # 检查命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg == "--process-data":
            # 专门处理process_data.py生成的文件
            return process_from_process_data_files()
        else:
            pile_id = arg
            print(f"[参数] 指定桩基ID: {pile_id}")
    else:
        print(f"[信息] 未指定桩基ID，将处理所有可用数据")
        print(f"[提示] 使用 --process-data 参数可专门处理process_data.py生成的文件")

    # 创建处理器
    processor = PileAnalysisProcessor(pile_id)

    # 运行完整分析
    success = processor.run_complete_analysis()

    if success:
        print(f"\n[完成] 桩基分析处理完成!")
    else:
        print(f"\n[失败] 桩基分析处理失败")

if __name__ == "__main__":
    main()
